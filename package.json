{"name": "amf-2-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@syncfusion/ej2-base": "^30.1.38", "@syncfusion/ej2-cldr-data": "^30.1.37", "@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "firebase": "^11.10.0", "idb-keyval": "^6.2.2", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuefire": "^3.2.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "^7.0.4"}}