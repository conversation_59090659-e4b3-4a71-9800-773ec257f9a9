# Vue 3 + Vite

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about IDE Support for Vue in the [Vue Docs Scaling up Guide](https://vuejs.org/guide/scaling-up/tooling.html#ide-support).

- For tests: "Minimum connection time"
- PErf metrics: smaller, below
- Bigger carousel boxes, bigger images, registration smaller and swap places with chips (ANDPC, ...)


Aurora:
- (done) Spreadsheet view - not updating
- (done)Be able to save as new - instead of overwriting
- (done)Scroll on the top - make it easy to scroll
- (done)X to close dialog
- (done)remove 'text' from translations
- (done)formation start is not the day of the course, but the earliest date
- (done)when presentation day is empty, use the start date
- (done)Filtering - simplify
- Sections delete is unintuitive
- (done)Duplicate formation - allow dates in the past + allow manual input
- (done)Duplicate formation - hours do not duplicate right
- Allow separate date/time columns in tables
- (done) Professions - make combo box bigger
- (done)Indemnisation - show in Euro

------------------------------
- Column reorder is broken (done)
- Table doesn't end where the web page ends (done)
- Ask user if they want to send email after registration
- Add user interventions in account (done)
- When course is full, hide from website
- Compte | Profession | Generaliste - should be changed to Medecin generaliste
- Add a separate 'Cancelled formations' (done)
- Add reports from old website

-------------------------------
- TODO list
- Auth with user/password
- May need to hide finished sections / not let users open old quizzes
- Télécharger le certificat -> Documents Disponible / A Telecharger
- Can we do a secure document signing on Aurora?
- Create course images
- L’expérience de la classe virtuelle -> nos formations / under notre equipe
- Mon Compte -> users can change, but we need to retain the old data; 
- - name, tel, addresses / upload: attestation FAF-PM , if intervenant -> other documents, CV, bank details (~banking identity)
- - new account: name, tel, email, address, 
- Registration - automatically: for FAF pm formation - attestation. If enough places, directly participant
- Aurora  - compact and mobile modes
- Comptes - inscriptions/interventions: make table configurable
- Differentiate test courses / checkbox active/not active

- Download the connection times
- The test screen is too small
- It should not duplicate the Zoom link
- MAsquer les sections - Voir les etapes de la formation
- Connection times
- Grading report templates on the website


- Protected view of formation summary
- Disable users with no activity for 1 year
- Grade out of 20
- Users can mark quiz as done
- Activity section - bigger formation name
- 
- Certificat de realisation: Organisateur, Numberinf
- ... No (numero) .... 
- Je sousigne is not bold. If organisateur/animateur, just organisateur / organisatrice
- date range: du vendredi 5 septembre au vendredi 5 septembre 2025 - all dates
- en Synchrone (format) not en visioconference - all documents (emerg/partic/...)
- signature - split half/half as shown
- numbers start at 2
Attestation: 
- Paris, le vendredi 5 septembre 2025
- organisateur / organisatrice with little o
- a assiste le vendredi 5 septembre 2025 en visioconference a la [only if is videoconference]
- No :eefdsfsd (no space :...)

- Adherent - signatures de l'organisateur ... break on multiple line


- Aurora - copy on a table cell copies the whole row
- Comptes - be able to add a new field
- Password DPC - make it visible
- Comptes - when cancel, show cancellation fields
- Cancel - uncheck participant

- Score out of 20 everywhere
- remove median
- add to header: question & % of correct answers
- on the post-test show the grade of the pre-test (in chip)
- add unit number to sections


1. Documents do not work well - how can we fix them?
2. Quiz: "Save my answers" + "Finish test"
3. When a test is open for correction, it is automatically blocked
4. With manual corrections, do not put 0 by default - put null
5. Corrections - keep the left list fixed
6. On website - GEAP, FAF, ANDPC - different colours
7. Satisfaction quiz - make it clear
8. Replace Mes Activites with Mes Formations / Mes Interventions
9. Confirmation emails with all formations he is registered for (by GEAP / FAF / ANDPC)