import {downloadStageImage} from "./services.js";


export function isOnline(formation) {
    return formation.lieudeformation_id == 117
}

export function getLocation(formation) {
    if (formation.lieudeformation_id == 117) {
        return "En ligne"
    } else {
        return formation.lieudeformation
    }
}

export async function loadStageImage(filename) {
    if (!filename) return null
  let image = null
  try {
    const blob = await downloadStageImage(filename);
    console.log(blob)
    if (!(blob instanceof Blob)) throw new Error("Response is not a Blob");
    image = URL.createObjectURL(blob);
  } catch (err) {
    console.error("Erreur de chargement de l’image", err);
    image = '';
  }
  return image
}