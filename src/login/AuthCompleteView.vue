<!-- src/views/AuthCompleteView.vue -->
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { getAuth } from 'firebase/auth'
import { useAuth } from './composables/useAuth'          // adjust path if needed
import { uca } from '../services'                        // adjust path if needed
import PasswordChangeForm from './ChangePasswordForm.vue'

const router = useRouter()
const { completeEmailLinkSignIn, updatePasswordAndClearFlag } = useAuth()

const loading = ref(true)
const error = ref<string | null>(null)
const message = ref('Finalisation de la connexion…')
const mustReset = ref(false)
const redirectTarget = ref<string>('/')

function sanitizeRedirect(raw: string | null | undefined): string | null {
  if (!raw) return null
  if (/^https?:\/\//i.test(raw) || raw.startsWith('//')) return null
  if (!raw.startsWith('/')) return null
  return raw.replace(/^\/+/, '/')
}

async function finalizeAndRedirect() {
  const auth = getAuth()
  const user = auth.currentUser
  if (!user) {
    error.value = 'Connexion incomplète. Veuillez réessayer.'
    return
  }

  // Optional approval check (kept from your code)
  let approved = false
  try { await uca(); approved = true } catch {}

  const emailVerified = user.emailVerified === true
  message.value = approved ? 'Connexion réussie. Redirection…' : 'Compte en attente de validation.'
  if (approved && emailVerified) {
    await router.replace(redirectTarget.value)
  } else {
    await router.replace({ name: 'auth-attente-validation' })
  }
}

async function onPasswordDone(newPassword: string) {
  // If your PasswordChangeForm updates itself, you can skip this call
  // and just do finalizeAndRedirect(). If it only collects the value:
  await updatePasswordAndClearFlag(newPassword, { reauthAfterUpdate: false })
  await finalizeAndRedirect()
}

function onPasswordCancel() {
  // Usually not allowed for enforced flow; you can route elsewhere if desired
  router.replace('/login')
}

function onPasswordError(msg: string) {
  error.value = msg
}

onMounted(async () => {
  try {
    const url = new URL(window.location.href)
    const rawNext = url.searchParams.get('redirect') || url.searchParams.get('next') || url.searchParams.get('r') || '/'
    redirectTarget.value = sanitizeRedirect(rawNext) ?? '/'

    const { ok, mustReset: needsReset } = await completeEmailLinkSignIn(window.location.href)
    if (!ok) {
      error.value = 'Lien invalide ou déjà utilisé.'
      return
    }

    if (needsReset) {
      mustReset.value = true
      message.value = 'Un dernier pas : définissez votre nouveau mot de passe.'
      return
    }

    await finalizeAndRedirect()
  } catch (e: any) {
    error.value = (e?.message || 'Une erreur est survenue.').replace('Firebase:', '').trim()
  } finally {
    loading.value = false
    window.history.replaceState({}, '', window.location.pathname)
  }
})
</script>

<template>
  <main class="max-w-md mx-auto p-6 text-center">
    <div class="rounded-xl border bg-white p-8 shadow">
      <h1 class="text-xl font-semibold mb-2">Connexion par lien</h1>

      <!-- Forced password change: center screen -->
      <div v-if="mustReset">
        <PasswordChangeForm
          context="system-enforce"
          :clear-flag="uca"
          :reauth-after-update="false"
          @done="onPasswordDone"
          @cancel="onPasswordCancel"
          @error="onPasswordError"
        />
      </div>

      <!-- Normal states -->
      <template v-else>
        <p v-if="loading" class="text-gray-600">
          {{ message }}
        </p>

        <div v-else>
          <p v-if="error" class="text-red-600 mb-4">{{ error }}</p>
          <p v-else class="text-green-700 mb-4">{{ message }}</p>

          <div class="flex justify-center gap-3">
            <router-link v-if="error" to="/login" class="underline text-primary">Retour à la connexion</router-link>
            <router-link v-else :to="redirectTarget" class="underline text-primary">Aller à l’accueil</router-link>
          </div>
        </div>
      </template>
    </div>
  </main>
</template>
