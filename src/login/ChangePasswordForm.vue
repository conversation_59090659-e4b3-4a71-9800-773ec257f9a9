<template>
  <form @submit.prevent="handleSubmit" class="space-y-4">
    <h3 class="text-lg font-semibold text-gray-900">{{ titleText }}</h3>
    <p class="text-sm text-gray-600 -mt-1 mb-2">{{ descText }}</p>

    <!-- Current password only when user requested a change -->
    <div v-if="needsCurrent">
      <label class="block text-sm font-medium text-gray-800 mb-1">Mot de passe actuel</label>
      <div class="relative">
        <input
          v-model="state.current"
          :type="state.showCurrent ? 'text' : 'password'"
          autocomplete="current-password"
          required
          class="w-full border border-gray-200 rounded-lg px-4 py-2 pr-12 focus:border-cyan-600 focus:ring-1 focus:ring-cyan-600 outline-none"
          :disabled="state.loading"
        />
        <button type="button" class="absolute right-2 top-1/2 -translate-y-1/2 text-xs sm:text-sm text-gray-600 hover:underline"
                @click="state.showCurrent = !state.showCurrent">
          {{ state.showCurrent ? 'Masquer' : 'Afficher' }}
        </button>
      </div>
    </div>

    <!-- New password -->
    <div>
      <label class="block text-sm font-medium text-gray-800 mb-1">Nouveau mot de passe</label>
      <div class="relative">
        <input
          ref="newInput"
          v-model="state.pass1"
          :type="state.show1 ? 'text' : 'password'"
          autocomplete="new-password"
          required
          class="w-full border border-gray-200 rounded-lg px-4 py-2 pr-12 focus:border-cyan-600 focus:ring-1 focus:ring-cyan-600 outline-none"
          :disabled="state.loading"
          placeholder="••••••••••••"
        />
        <button type="button" class="absolute right-2 top-1/2 -translate-y-1/2 text-xs sm:text-sm text-gray-600 hover:underline"
                @click="state.show1 = !state.show1">
          {{ state.show1 ? 'Masquer' : 'Afficher' }}
        </button>
      </div>
    </div>

    <!-- Strength + rules -->
    <div class="mt-1">
      <div class="h-2 w-full bg-gray-200 rounded-full overflow-hidden" aria-hidden="true">
        <div class="h-full bg-cyan-600 transition-all" :style="{ width: ((strength / 5) * 100) + '%' }"></div>
      </div>
      <p class="mt-1 text-xs text-gray-500">Solidité : {{ strength }}/5</p>
    </div>
    <ul class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-1 text-xs">
      <li :class="policy.lengthOk ? 'text-green-600' : 'text-gray-600'">• {{ minLength }} caractères minimum</li>
      <li :class="categoryCount >= requiredCategories ? 'text-green-600' : 'text-gray-600'">
        • Au moins {{ requiredCategories }} sur 4 : majuscule, minuscule, chiffre, spécial
      </li>
    </ul>

    <!-- Confirm -->
    <div>
      <label class="block text-sm font-medium text-gray-800 mb-1">Confirmer le nouveau mot de passe</label>
      <div class="relative">
        <input
          v-model="state.pass2"
          :type="state.show2 ? 'text' : 'password'"
          autocomplete="new-password"
          required
          class="w-full border border-gray-200 rounded-lg px-4 py-2 pr-12 focus:border-cyan-600 focus:ring-1 focus:ring-cyan-600 outline-none"
          :disabled="state.loading"
          placeholder="••••••••••••"
        />
        <button type="button" class="absolute right-2 top-1/2 -translate-y-1/2 text-xs sm:text-sm text-gray-600 hover:underline"
                @click="state.show2 = !state.show2">
          {{ state.show2 ? 'Masquer' : 'Afficher' }}
        </button>
      </div>
    </div>

    <p v-if="state.error" class="text-red-600 text-sm" role="alert" aria-live="assertive">{{ state.error }}</p>

    <div class="mt-3 flex items-center justify-end gap-2">
      <button
        v-if="allowCancel"
        type="button"
        class="inline-flex items-center justify-center rounded-lg border border-gray-300 bg-white text-gray-700 font-medium px-4 py-2 hover:bg-gray-50"
        :disabled="state.loading"
        @click="$emit('cancel')">
        Annuler
      </button>
      <button
        type="submit"
        class="inline-flex items-center justify-center rounded-lg bg-cyan-600 hover:bg-cyan-700 text-white font-semibold px-4 py-2 disabled:opacity-60"
        :disabled="state.loading || !isValid">
        <span v-if="state.loading">{{ busyLabel }}</span>
        <span v-else>{{ submitLabel }}</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, nextTick } from 'vue'
import {
  getAuth, EmailAuthProvider, reauthenticateWithCredential,
  updatePassword, reload
} from 'firebase/auth'

type Context = 'user-request' | 'system-enforce' | 'new-user-setup'

const props = withDefaults(defineProps<{
  context: 'user-request' | 'system-enforce' | 'new-user-setup'
  clearFlag?: () => Promise<void>
  minLength?: number
  requiredCategories?: number
  reauthAfterUpdate?: boolean
  collectOnly?: boolean
}>(), {
  minLength: 8,
  requiredCategories: 3,
  reauthAfterUpdate: false,
  collectOnly: false,
})

const emit = defineEmits<{ (e: 'done'): void; (e: 'cancel'): void; (e: 'error', message: string): void }>()

const newInput = ref<HTMLInputElement|null>(null)
const state = reactive({
  current: '',
  pass1: '',
  pass2: '',
  showCurrent: false,
  show1: false,
  show2: false,
  loading: false,
  error: '',
})

const needsCurrent = computed(() => props.context === 'user-request')
const allowCancel = computed(() => props.context !== 'system-enforce')

const titleText = computed(() => ({
  'user-request':   'Changer le mot de passe',
  'system-enforce': 'Définir un nouveau mot de passe',
  'new-user-setup': 'Créer un mot de passe',
}[props.context]))

const descText = computed(() => ({
  'user-request':   'Veuillez confirmer votre mot de passe actuel et définir un nouveau mot de passe.',
  'system-enforce': 'Pour continuer, vous devez choisir un nouveau mot de passe.',
  'new-user-setup': 'Bienvenue ! Créez le mot de passe de votre compte.',
}[props.context]))

const policy = computed(() => ({
  lengthOk: state.pass1.length >= props.minLength!,
  upper: /[A-Z]/.test(state.pass1),
  lower: /[a-z]/.test(state.pass1),
  digit: /\d/.test(state.pass1),
  special: /[^A-Za-z0-9]/.test(state.pass1),
}))
const categoryCount = computed(() => (policy.value.upper?1:0) + (policy.value.lower?1:0) + (policy.value.digit?1:0) + (policy.value.special?1:0))
const strength = computed(() => (policy.value.lengthOk?1:0) + (policy.value.upper?1:0) + (policy.value.lower?1:0) + (policy.value.digit?1:0) + (policy.value.special?1:0))
const isValid = computed(() => {
  const base = policy.value.lengthOk && categoryCount.value >= props.requiredCategories! && state.pass1 === state.pass2
  return needsCurrent.value ? base && !!state.current : base
})

const submitLabel = computed(() =>
  props.context === 'user-request' ? 'Changer le mot de passe'
  : props.context === 'system-enforce' ? 'Mettre à jour'
  : 'Créer le mot de passe'
)
const busyLabel = computed(() => (props.context === 'user-request' ? 'Mise à jour…' : 'Enregistrement…'))

onMounted(async () => { await nextTick(); newInput.value?.focus() })

async function handleSubmit() {
  state.error = ''
  if (!isValid.value) {
    state.error = 'Veuillez respecter les critères et confirmer le mot de passe.'
    return
  }

  if (props.collectOnly) {
    emit('done', state.pass1)    // <-- just pass the password up
    return
  }

  state.loading = true
  try {
    const auth = getAuth()
    const user = auth.currentUser
    if (!user || !user.email) throw new Error('Vous devez être connecté.')

    if (needsCurrent.value) {
      const cred = EmailAuthProvider.credential(user.email, state.current)
      await reauthenticateWithCredential(user, cred)
    }

    await updatePassword(user, state.pass1)

    // For enforce/setup, clear server-side flag if provided
    if (props.context !== 'user-request' && props.clearFlag) {
      try { await props.clearFlag() } catch {}
    }

    if (props.reauthAfterUpdate && user.email) {
      try {
        const credNew = EmailAuthProvider.credential(user.email, state.pass1)
        await reauthenticateWithCredential(user, credNew)
      } catch {}
    }

    await reload(user)
    await user.getIdToken(true)

    state.current = state.pass1 = state.pass2 = ''
    emit('done')
  } catch (e: any) {
    const msg = (e?.message || 'Une erreur est survenue.').replace('Firebase:', '').trim()
    state.error = msg
    emit('error', msg)
  } finally {
    state.loading = false
  }
}
</script>
