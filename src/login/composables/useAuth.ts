// src/components/composables/useAuth.ts
import {
  getAuth,
  setPersistence,
  browserLocalPersistence,
  type Persistence,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  sendSignInLinkToEmail,
  isSignInWithEmailLink,
  signInWithEmailLink,
  signInWithCustomToken,
  getIdTokenResult,
  updatePassword,
  reload,
  signOut as fbSignOut,
  type ActionCodeSettings,
  type User,
} from 'firebase/auth'
import { uc, uca } from '../../services'

export function authErrorMessage(e: unknown) {
  const code = (e as any)?.code as string | undefined
  switch (code) {
    case 'auth/invalid-email': return 'Veuillez saisir une adresse email valide.'
    case 'auth/user-disabled': return 'Ce compte a été désactivé.'
    case 'auth/user-not-found':
    case 'auth/wrong-password': return "Identifiant ou mot de passe incorrect."
    case 'auth/weak-password': return 'Mot de passe trop faible.'
    case 'auth/email-already-in-use': return 'Cet email est déjà utilisé.'
    case 'auth/too-many-requests': return 'Trop de tentatives. Réessayez plus tard.'
    default: return 'Une erreur est survenue. Veuillez réessayer.'
  }
}

type UcResult =
  | { code: 'MUST_RESET' | 'ALREADY_MIGRATED' | 'NO_MIGRATION'; customToken?: string }
  | { code: '2' | '1' | '0'; customToken?: string }

function normalizeEmail(email: string) { return email.trim().toLowerCase() }
function normalizeUcCode(code: string): 'MUST_RESET' | 'ALREADY_MIGRATED' | 'NO_MIGRATION' {
  if (code === '2' || code === 'MUST_RESET') return 'MUST_RESET'
  if (code === '1' || code === 'ALREADY_MIGRATED') return 'ALREADY_MIGRATED'
  return 'NO_MIGRATION'
}

export function useAuth(opts: {
  persistence?: Persistence
  emailLinkUrl?: string
  resetPasswordUrl?: string
  emailStorageKey?: string
} = {}) {
  const auth = getAuth()
  setPersistence(auth, opts.persistence ?? browserLocalPersistence).catch(() => {})

  const STORAGE_KEY = opts.emailStorageKey ?? 'emailForSignIn'
  const emailLinkActionCode: ActionCodeSettings = {
    url: `${window.location.origin}/auth/complete`,
    handleCodeInApp: true,
  }

  const resetActionCode: ActionCodeSettings = {
    // old: url: opts.resetPasswordUrl ?? `${window.location.origin}/login`,
    url: `${window.location.origin}/login`,
  }

  // ---------- MAGIC LINK ----------
  async function startEmailLinkSignIn(email: string): Promise<void> {
    const normalized = normalizeEmail(email)
    await uc(normalized, '') // optional precheck
    await sendSignInLinkToEmail(auth, normalized, emailLinkActionCode)
    window.localStorage.setItem(STORAGE_KEY, normalized)
  }

  async function completeEmailLinkSignIn(currentUrl: string): Promise<{ ok: boolean; mustReset: boolean }> {
    if (!isSignInWithEmailLink(auth, currentUrl)) return { ok: false, mustReset: false }
    let email = window.localStorage.getItem(STORAGE_KEY)
    if (!email) email = window.prompt('Veuillez confirmer votre email pour vous connecter') || ''
    const cred = await signInWithEmailLink(auth, email, currentUrl)
    window.localStorage.removeItem(STORAGE_KEY)
    await cred.user.getIdToken(true)
    const { claims } = await getIdTokenResult(cred.user, true)
    const mustReset = !!(claims as any)?.must_reset_password
    return { ok: true, mustReset }
  }

  // ---------- PASSWORD ----------
  async function signInWithPasswordOrBridge(
    identifier: string,
    password: string,
    bridge: (identifier: string, password: string) => Promise<UcResult>,
  ): Promise<{ ok: true; mustReset: boolean }> {
    try {
      const email = identifier.includes('@') ? normalizeEmail(identifier) : normalizeEmail(identifier)
      const cred = await signInWithEmailAndPassword(auth, email, password)
      await cred.user.getIdToken(true)
      const { claims } = await getIdTokenResult(cred.user, true)
      const mustReset = !!(claims as any)?.must_reset_password
      return { ok: true, mustReset }
    } catch (e: any) {
      const code = e?.code || ''
      if (code !== 'auth/user-not-found' && code !== 'auth/wrong-password') throw e
      const res = await bridge(identifier, password)
      const status = normalizeUcCode((res as any).code)
      if (status === 'MUST_RESET' && res.customToken) {
        const cred = await signInWithCustomToken(auth, res.customToken)
        await cred.user.getIdToken(true)
        // must_reset is expected now
        return { ok: true, mustReset: true }
      }
      // ALREADY_MIGRATED or NO_MIGRATION -> keep original error
      throw e
    }
  }

  // Finalize reset (used by AuthView after collecting new password)
  async function updatePasswordAndClearFlag(newPassword: string, opts?: { reauthAfterUpdate?: boolean }): Promise<void> {
    const user = auth.currentUser
    if (!user) throw new Error('Not signed in')
    await updatePassword(user, newPassword)
    try { await uca() } catch { /* non-fatal */ }
    await reload(user)
    await user.getIdToken(true)
    if (opts?.reauthAfterUpdate && user.email) {
      // optional; usually not necessary for magic-link / token login
      try {
        // Reauth with new password; ignore if provider mismatch
        const { EmailAuthProvider, reauthenticateWithCredential } = await import('firebase/auth')
        // @ts-ignore dynamic import types
        const cred = EmailAuthProvider.credential(user.email, newPassword)
        // @ts-ignore dynamic import types
        await signInWithEmailAndPassword(auth, user.email, newPassword)
        //await reauthenticateWithCredential(user, cred)
        await uca()
      } catch {}
    }
  }

  // ---------- Misc ----------
  async function sendPasswordReset(identifier: string) {
    const email = normalizeEmail(identifier)
    await sendPasswordResetEmail(auth, email, resetActionCode)
  }
  function currentUser(): User | null { return auth.currentUser }
  async function signOut() { await fbSignOut(auth) }

  return {
    // magic link
    startEmailLinkSignIn,
    completeEmailLinkSignIn,
    // password login (headless)
    signInWithPasswordOrBridge,
    updatePasswordAndClearFlag,
    // misc
    sendPasswordReset,
    currentUser,
    signOut,
    authErrorMessage,
  }
}
