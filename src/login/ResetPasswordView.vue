<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  getAuth,
  verifyPasswordResetCode,
  confirmPasswordReset,
  signInWithEmailAndPassword,
} from 'firebase/auth'
import PasswordChangeForm from './ChangePasswordForm.vue'

const router = useRouter()
const loading = ref(true)
const error = ref<string | null>(null)
const email = ref<string>('')         // email tied to the reset code
const oobCode = ref<string>('')       // reset token
const message = ref('Vérification du lien…')

// optional: auto sign-in after reset
const AUTO_SIGN_IN = true

function sanitizeRedirect(raw?: string | null) {
  if (!raw) return null
  if (/^https?:\/\//i.test(raw) || raw.startsWith('//')) return null
  if (!raw.startsWith('/')) return null
  return raw.replace(/^\/+/, '/')
}
const redirectTarget = ref<string>('/')

async function onSubmitNewPassword(newPassword: string) {
  error.value = null
  const auth = getAuth()
  await confirmPasswordReset(auth, oobCode.value, newPassword)

  if (AUTO_SIGN_IN && email.value) {
    try {
      await signInWithEmailAndPassword(auth, email.value, newPassword)
    } catch {
      // If sign-in fails (e.g. disabled), just continue to login.
    }
  }

  router.replace(redirectTarget.value || '/')
}

onMounted(async () => {
  try {
    const url = new URL(window.location.href)
    const mode = url.searchParams.get('mode')
    const code = url.searchParams.get('oobCode')
    const cont = url.searchParams.get('continueUrl')
    redirectTarget.value = sanitizeRedirect(cont) ?? '/'

    if (mode !== 'resetPassword' || !code) {
      error.value = 'Lien invalide.'
      return
    }

    oobCode.value = code
    const auth = getAuth()
    email.value = await verifyPasswordResetCode(auth, code) // throws if invalid/expired
    message.value = 'Définissez un nouveau mot de passe pour votre compte.'
  } catch (e: any) {
    error.value = (e?.message || 'Lien invalide ou expiré.').replace('Firebase:', '').trim()
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <main class="max-w-md mx-auto p-6 text-center">
    <div class="rounded-xl border bg-white p-8 shadow">
      <h1 class="text-xl font-semibold mb-2">Réinitialiser le mot de passe</h1>

      <p v-if="loading" class="text-gray-600">{{ message }}</p>

      <div v-else>
        <p v-if="error" class="text-red-600 mb-4">{{ error }}</p>

        <div v-else>
          <p class="text-gray-700 mb-4">Adresse : <strong>{{ email }}</strong></p>

          <!-- Collect-only: component validates UI, parent does confirmPasswordReset -->
          <PasswordChangeForm
            context="new-user-setup"
            :collect-only="true"
            :reauth-after-update="false"
            @done="onSubmitNewPassword"
          />
        </div>

        <div class="mt-4" v-if="error">
          <router-link to="/login" class="underline text-primary">Retour à la connexion</router-link>
        </div>
      </div>
    </div>
  </main>
</template>
