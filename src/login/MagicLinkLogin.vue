<template>
  <form @submit.prevent="onSubmit" class="space-y-4">
    <label class="block text-sm font-medium text-primary-dark mb-1">Email</label>
    <input
      id="magic-email"
      name="email"
      v-model="email"
      type="email"
      autocomplete="email"
      autocapitalize="off"
      spellcheck="false"
      inputmode="email"
      required
      class="w-full border border-gray-200 rounded-lg px-4 py-2 bg-bg focus:border-primary focus:ring-1 focus:ring-primary outline-none"
      :disabled="loading"
      placeholder="Votre email"
    />

    <div v-if="error" class="text-red-600 text-sm text-center" role="alert" aria-live="assertive">{{ error }}</div>
    <div v-if="info" class="text-green-600 text-sm text-center" role="status" aria-live="polite">{{ info }}</div>

    <button
      :disabled="loading || !email"
      type="submit"
      class="w-full bg-cyan-600 hover:bg-cyan-700 text-white font-bold py-2.5 rounded-lg shadow transition disabled:opacity-70"
    >
      <span v-if="loading">Envoi du lien…</span>
      <span v-else>Envoyer le lien</span>
    </button>
  </form>

</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue'
import { useAuth } from './composables/useAuth'

const props = defineProps<{ initialEmail?: string }>()

const emit = defineEmits<{ (e: 'link-sent'): void }>()
const { startEmailLinkSignIn, authErrorMessage } = useAuth()

const email = ref('')
const loading = ref(false)
const error = ref('')
const info = ref('')

async function onSubmit() {
  error.value = ''
  info.value = ''
  loading.value = true
  try {
    await startEmailLinkSignIn(email.value)
    info.value = "Si votre compte est éligible, nous vous avons envoyé un lien de connexion."
    emit('link-sent')
  } catch (e: any) {
    //error.value = authErrorMessage(e) || e?.message || 'Une erreur est survenue.'
  } finally {
    info.value = "Si votre compte est éligible, nous vous avons envoyé un lien de connexion."
    loading.value = false
  }
}

onMounted(() => {
  if (props.initialEmail) email.value = props.initialEmail
})
</script>
