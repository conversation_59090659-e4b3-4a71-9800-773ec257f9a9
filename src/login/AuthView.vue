<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MagicLinkLogin from './MagicLinkLogin.vue'
import PasswordLogin from './PasswordLogin.vue'
import RegisterForm from './RegisterForm.vue'
import ForgotPasswordForm from './ForgotPasswordForm.vue'
import ChangePasswordForm from './ChangePasswordForm.vue' // inline form, not a dialog
import { useAuth } from './composables/useAuth'

type View = 'login' | 'register' | 'forgot' | 'password'
type LoginMode = 'magic' | 'password'
type ChangeContext = 'system-enforce' | 'new-user-setup' | 'user-request'

const route = useRoute()
const router = useRouter()
const { updatePasswordAndClearFlag } = useAuth()

const view = ref<View>('login')
const loginMode = ref<LoginMode>('password')
const forgotPrefill = ref<string>('')
const magicPrefill = ref<string>('')

const changeContext = ref<ChangeContext>('system-enforce')

function onRegisterSuccess(email: string) {
  // If new users must set a password immediately, go to password view:
  // changeContext.value = 'new-user-setup'; view.value = 'password'
  // Otherwise fall back to login:
  view.value = 'login'
  loginMode.value = 'magic'
  magicPrefill.value = email
}
function switchMode(mode: LoginMode) { if (loginMode.value !== mode) loginMode.value = mode }

function sanitizeRedirect(raw?: string | null) {
  if (!raw) return null
  if (/^https?:\/\//i.test(raw) || raw.startsWith('//')) return null
  if (!raw.startsWith('/')) return null
  return raw.replace(/^\/+/, '/')
}
const redirectTarget = computed(() => {
  const q = route.query
  const raw = (q.redirect as string) || (q.next as string) || (q.r as string) || '/'
  return sanitizeRedirect(raw) ?? '/'
})

async function handlePasswordSubmit(newPassword: string) {
  await updatePasswordAndClearFlag(newPassword, {
    reauthAfterUpdate: changeContext.value === 'user-request',
  })
  // after success, go to the intended target
  router.replace(redirectTarget.value)
}
function handlePasswordCancel() {
  // For system-enforce you may want to disallow cancel; otherwise just go back:
  view.value = 'login'
  loginMode.value = 'password'
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-bg px-4 py-8">
    <transition name="fade-slide" mode="out-in">
      <div class="w-full max-w-md bg-white rounded-2xl shadow-xl p-8 min-h-[600px]" :key="view">
        <!-- Header -->
        <div class="flex flex-col items-center mb-7 h-full">
          <img src="/images/logo.png" alt="AMF" class="h-14 mb-2 drop-shadow-md" />
          <h2 class="text-2xl font-bold text-primary-dark mb-1">
            <span v-if="view === 'login'">Connexion</span>
            <span v-else-if="view === 'register'">Créer un compte</span>
            <span v-else-if="view === 'forgot'">Mot de passe oublié</span>
            <span v-else-if="view === 'password'">
              {{ changeContext === 'new-user-setup' ? 'Créer un mot de passe' : 'Nouveau mot de passe' }}
            </span>
          </h2>
          <p class="text-sm text-gray-500" v-if="view === 'login'">
            Renseignez vos identifiants pour accéder à votre espace.
          </p>
        </div>

        <!-- LOGIN VIEW -->
        <div v-if="view === 'login'">
          <!--div class="grid grid-cols-2 gap-2 text-xs sm:text-sm leading-snug mb-4">

            <button
              type="button"
              class="py-2 rounded-lg font-medium border transition"
              :class="loginMode === 'password'
                ? 'bg-cyan-600 text-white border-cyan-600'
                : 'bg-white text-gray-700 border-gray-200 hover:border-gray-300'"
              @click="switchMode('password')"
              :aria-pressed="loginMode === 'password'">
              Email + mot de passe
            </button>

            <button
              type="button"
              class="py-2 rounded-lg font-medium border transition"
              :class="loginMode === 'magic'
                ? 'bg-cyan-600 text-white border-cyan-600'
                : 'bg-white text-gray-700 border-gray-200 hover:border-gray-300'"
              @click="switchMode('magic')"
              :aria-pressed="loginMode === 'magic'">
              Lien par email
            </button>
          </div-->

          <PasswordLogin
            @forgot-password="(id) => { forgotPrefill = (id ?? ''); view = 'forgot' }"
            @must-reset="() => { changeContext = 'system-enforce'; view = 'password' }"
            @login-success="router.replace(redirectTarget)"
          />

          <div class="flex flex-wrap items-center justify-between gap-2 text-xs sm:text-sm mt-3 text-center">
            <button type="button" class="text-primary hover:underline font-medium px-0 bg-transparent border-0"
                    @click.prevent="view = 'register'" tabindex="0">
              Créer un compte
            </button>
          </div>
        </div>

        <!-- REGISTER VIEW -->
        <RegisterForm
          v-else-if="view === 'register'"
          @register-success="onRegisterSuccess"
          @login="view = 'login'"
        />

        <!-- FORGOT PASSWORD VIEW -->
        <div v-else-if="view === 'forgot'">
          <ForgotPasswordForm :initial-identifier="forgotPrefill" @reset-sent="view = 'login'" />
          <div class="text-center mt-4">
            <button type="button" class="text-primary hover:underline text-sm bg-transparent border-0"
                    @click="view = 'login'">
              Retour à la connexion
            </button>
          </div>
        </div>

        <!-- PASSWORD CHANGE / CREATE VIEW (center content) -->
        <div v-else-if="view === 'password'">
          <ChangePasswordForm
            :context="changeContext"
            :clear-flag="changeContext !== 'user-request' ? () => Promise.resolve() : undefined"
            :reauth-after-update="changeContext === 'user-request'"
            @done="handlePasswordSubmit"
            @cancel="handlePasswordCancel"
          />
        </div>
      </div>
    </transition>
  </div>
</template>

<style>
.fade-slide-enter-active,
.fade-slide-leave-active { transition: all 0.3s cubic-bezier(.43,.13,.23,.96); }
.fade-slide-enter-from { opacity: 0; transform: translateY(16px) scale(0.97); }
.fade-slide-enter-to { opacity: 1; transform: translateY(0) scale(1); }
.fade-slide-leave-from { opacity: 1; transform: translateY(0) scale(1); }
.fade-slide-leave-to { opacity: 0; transform: translateY(-16px) scale(0.97); }
</style>
