<template>
  <form @submit.prevent="onSubmit" class="space-y-4">
    <div>
      <label class="block text-sm font-medium text-primary-dark mb-1">Email ou nom d’utilisateur</label>
      <input
        id="login-username"
        name="email"
        v-model="identifier"
        type="text"
        autocomplete="section-login username"
        autocapitalize="off"
        spellcheck="false"
        inputmode="email"
        required
        class="w-full border border-gray-200 rounded-lg px-4 py-2 bg-bg focus:border-primary focus:ring-1 focus:ring-primary outline-none"
        :disabled="loading"
        placeholder="Email ou nom d’utilisateur"
      />
    </div>

    <div>
      <label class="block text-sm font-medium text-primary-dark mb-1">Mot de passe</label>
      <div class="relative">
        <input
          v-model="password"
          :type="showPassword ? 'text' : 'password'"
          autocomplete="current-password"
          required
          class="w-full border border-gray-200 rounded-lg px-4 py-2 pr-12 bg-bg focus:border-primary focus:ring-1 focus:ring-primary outline-none"
          :disabled="loading"
          placeholder="Votre mot de passe"
        />
        <button
          type="button"
          class="absolute right-2 top-1/2 -translate-y-1/2 text-xs sm:text-sm text-gray-600 hover:underline"
          @click="showPassword = !showPassword"
          :disabled="loading"
          aria-label="Afficher/masquer le mot de passe"
        >
          {{ showPassword ? 'Masquer' : 'Afficher' }}
        </button>
      </div>
    </div>

    <div class="text-right">
      <button
        type="button"
        class="text-primary hover:underline text-xs sm:text-sm bg-transparent border-0 px-0"
        @click="$emit('forgot-password', identifier)"
        :disabled="loading"
      >
        Mot de passe oublié ?
      </button>
    </div>

    <div v-if="error" class="text-red-600 text-sm text-center" role="alert" aria-live="assertive">{{ error }}</div>
    <div v-if="info" class="text-green-600 text-sm text-center" role="status" aria-live="polite">{{ info }}</div>

    <button
      :disabled="loading || !(identifier && password)"
      type="submit"
      class="w-full bg-cyan-600 hover:bg-cyan-700 text-white font-bold py-2.5 rounded-lg shadow transition disabled:opacity-70"
    >
      <span v-if="loading">Connexion…</span>
      <span v-else>Se connecter</span>
    </button>
  </form>
  <teleport to="body">
    <div
      v-if="loading"
      class="fixed inset-0 z-[1000] flex items-center justify-center bg-black/40 backdrop-blur-sm"
      role="dialog"
      aria-modal="true"
      aria-label="Connexion en cours"
    >
      <div class="flex flex-col items-center gap-3 rounded-xl bg-white/90 p-6 shadow-lg">
        <div class="h-10 w-10 animate-spin rounded-full border-4 border-gray-300 border-t-transparent"></div>
        <p class="text-sm text-gray-700">Connexion…</p>
      </div>
      <span class="sr-only">Veuillez patienter pendant la connexion</span>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuth } from './composables/useAuth'
import { uc } from '../services'

const emit = defineEmits<{
  (e: 'login-success'): void
  (e: 'forgot-password', identifier?: string): void
  (e: 'must-reset'): void
}>()

const { signInWithPasswordOrBridge, authErrorMessage } = useAuth()

const identifier = ref('')
const password = ref('')
const showPassword = ref(false)
const loading = ref(false)
const error = ref(''); const info = ref('')

async function onSubmit() {
  error.value = ''; info.value = ''; loading.value = true
  try {
    const res = await signInWithPasswordOrBridge(identifier.value, password.value, uc)
    if (res.mustReset) {
      emit('must-reset')           // parent shows dialog and completes reset
      return
    }
    info.value = 'Connexion réussie.'
    emit('login-success')
  } catch (e: any) {
    error.value = authErrorMessage(e) || (e?.message || 'Une erreur est survenue.')
  } finally {
    loading.value = false
  }
}
</script>

