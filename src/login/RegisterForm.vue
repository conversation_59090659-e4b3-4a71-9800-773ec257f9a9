<template>
  <form @submit.prevent="onRegister" class="space-y-4">
    <p class="text-sm text-slate-600 -mt-2">
      <PERSON><PERSON><PERSON> de renseigner vos informations. <strong>Email</strong> et <strong>téléphone</strong> doivent être valides ;
      nous vous contacterons pour <strong>valider votre compte</strong>.
    </p>

    <!-- Honeypot (server-validated; keep hidden) -->
    <div class="hidden" aria-hidden="true">
      <label for="hp-company">Société (ne pas remplir)</label>
      <input
        id="hp-company"
        v-model="honeypot"
        type="text"
        tabindex="-1"
        autocomplete="new-password"
        aria-hidden="true"
      />
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div>
        <label for="register-firstname" class="block text-base font-semibold text-slate-700 mb-1">Prénom</label>
        <input
          id="register-firstname"
          name="given-name"
          v-model.trim="firstName"
          type="text"
          required
          autocomplete="given-name"
          :disabled="loading || canProceed"
          class="w-full border border-slate-200 rounded-lg px-4 py-2.5 bg-slate-50 text-slate-800 focus:outline-none focus:border-cyan-600 transition"
          placeholder="Votre prénom"
        />
      </div>
      <div>
        <label for="register-lastname" class="block text-base font-semibold text-slate-700 mb-1">Nom</label>
        <input
          id="register-lastname"
          name="family-name"
          v-model.trim="lastName"
          type="text"
          required
          autocomplete="family-name"
          :disabled="loading || canProceed"
          class="w-full border border-slate-200 rounded-lg px-4 py-2.5 bg-slate-50 text-slate-800 focus:outline-none focus:border-cyan-600 transition"
          placeholder="Votre nom"
        />
      </div>
    </div>

    <div>
      <label for="register-email" class="block text-base font-semibold text-slate-700 mb-1">Email</label>
      <input
        id="register-email"
        name="email"
        v-model.trim="email"
        type="email"
        autocomplete="email"
        autocapitalize="off"
        spellcheck="false"
        required
        :disabled="loading || canProceed"
        class="w-full border border-slate-200 rounded-lg px-4 py-2.5 bg-slate-50 text-slate-800 focus:outline-none focus:border-cyan-600 transition"
        placeholder="<EMAIL>"
      />
      <p class="mt-1 text-xs text-slate-500">Utilisez une adresse que vous consultez régulièrement.</p>
    </div>

    <div>
      <label for="register-phone" class="block text-base font-semibold text-slate-700 mb-1">Téléphone</label>
      <input
        id="register-phone"
        name="tel"
        v-model.trim="phone"
        type="tel"
        inputmode="tel"
        autocomplete="tel"
        required
        :disabled="loading || canProceed"
        class="w-full border border-slate-200 rounded-lg px-4 py-2.5 bg-slate-50 text-slate-800 focus:outline-none focus:border-cyan-600 transition"
        placeholder="+33 6 12 34 56 78"
      />
      <p class="mt-1 text-xs text-slate-500">Indiquez un numéro joignable (portable de préférence).</p>
    </div>

    <div class="flex items-start gap-2">
      <input id="terms" v-model="termsAccepted" type="checkbox" required class="mt-1" :disabled="loading || canProceed"/>
      <label for="terms" class="text-sm text-slate-700">
        J’accepte les <router-link to="/cgv" target="_blank" rel="noopener" class="text-cyan-700 hover:underline">Conditions d’utilisation</router-link> et la
        <router-link to="/politiquesdeconfidentialite" target="_blank" rel="noopener" class="text-cyan-700 hover:underline">Politique de confidentialité</router-link>.
      </label>
    </div>

    <!-- Messages -->
    <div v-if="error" class="text-red-500 font-medium text-sm" aria-live="assertive">{{ error }}</div>
    <div v-if="info" class="text-green-700 font-medium text-sm" aria-live="polite">{{ info }}</div>

    <!-- Primary action -->
    <button
      v-if="!canProceed"
      :disabled="loading"
      type="submit"
      class="w-full bg-cyan-600 hover:bg-cyan-700 text-white font-bold py-2.5 rounded-lg shadow transition disabled:opacity-70 disabled:pointer-events-none"
      :aria-busy="loading ? 'true' : 'false'"
    >
      <span v-if="loading">Envoi de la demande…</span>
      <span v-else>Envoyer ma demande</span>
    </button>

    <!-- Proceed CTA shown after backend returns (200/201/409) -->
    <button
      v-else
      type="button"
      class="w-full bg-cyan-600 hover:bg-cyan-700 text-white font-bold py-2.5 rounded-lg shadow transition"
      @click="$emit('register-success', normalizedEmail)"
    >
      Allez à la page de connexion pour confirmer votre adresse e-mail et définir un mot de passe.
    </button>

    <div class="mt-2 text-center" v-if="!canProceed">
      <a
        @click.prevent="$emit('login')"
        href="#"
        class="text-cyan-700 hover:underline hover:text-cyan-900 font-medium transition"
      >
        Déjà un compte ?
      </a>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { register } from '../services'

const firstName = ref('')
const lastName  = ref('')
const email     = ref('')
const phone     = ref('')
const termsAccepted = ref(false)
const honeypot  = ref('') // sent to server; backend handles silently

const error   = ref('')
const info    = ref('')
const loading = ref(false)
const canProceed = ref(false)

const emit = defineEmits<{
  (e: 'register-success'): void
  (e: 'login'): void
  (e: 'go-magic', email: string): void
}>()

const normalizedEmail = computed(() => (email.value || '').trim().toLowerCase())

function isValidEmail(v: string) {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v)
}
function isValidPhone(v: string) {
  const digits = (v || '').replace(/\D/g, '')
  return digits.length >= 8 && digits.length <= 16
}
function normalizePhone(v: string) {
  return (v || '').replace(/[^\d+]/g, '')
}
function createIdempotencyKey(): string {
  // @ts-ignore
  return (typeof crypto !== 'undefined' && crypto.randomUUID)
    // @ts-ignore
    ? crypto.randomUUID()
    : `reg-${Date.now()}-${Math.random().toString(36).slice(2)}${Math.random().toString(36).slice(2)}`
}

async function onRegister() {
  error.value = ''
  info.value  = ''

  // Basic client-side checks
  if (!firstName.value || !lastName.value) {
    error.value = "Merci d’indiquer votre prénom et votre nom."
    return
  }
  if (!isValidEmail(email.value)) {
    error.value = "L’email semble invalide."
    return
  }
  if (!isValidPhone(phone.value)) {
    error.value = "Le numéro de téléphone semble invalide."
    return
  }
  if (!termsAccepted.value) {
    error.value = "Vous devez accepter les conditions pour continuer."
    return
  }

  loading.value = true
  try {
    const payload = {
      firstName: firstName.value.trim(),
      lastName : lastName.value.trim(),
      email    : normalizedEmail.value,
      phone    : normalizePhone(phone.value),
      hp       : honeypot.value,
    }
    const res: Response = await register(payload)

    // Treat 200/201/409 as “proceed to magic link page” (no enumeration).
    if (res.status === 201) {
      info.value = "Compte enregistré. Continuez vers la page de connexion pour recevoir votre lien d’accès."
      canProceed.value = true
    } else if (res.status === 200) {
      info.value = "Si un compte est associé à cet email, utilisez la page de connexion pour obtenir un lien d’accès."
      canProceed.value = true
    } else if (res.status === 409) {
      info.value = "Un compte avec cet email existe peut-être déjà. Utilisez la page de connexion pour recevoir un lien d’accès."
      canProceed.value = true
    } else {
      const text = await res.text()
      throw new Error(text || 'Échec de l’inscription. Réessayez.')
    }


  } catch (e: any) {
    error.value = String(e?.message || e)
  } finally {
    loading.value = false
  }
}
</script>
