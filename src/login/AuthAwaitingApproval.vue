<!-- src/views/AwaitingApproval.vue -->
<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getAuth, sendEmailVerification, signOut } from 'firebase/auth'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const checking = ref(false)
const error = ref<string | null>(null)
const email = ref<string | null>(null)
const emailVerified = ref<boolean | null>(null)

// Optional: lightweight polling so the page auto-advances once the admin approves
let timer: number | null = null
const POLL_MS = 30000 // 30s

// Sanitize a "next" param if present (defensive)
function nextPath() {
  const n = String(route.query.next || '/')
  return n.startsWith('/') && !n.startsWith('//') ? n : '/'
}

async function doLogout() {
  const auth = getAuth()
  await signOut(auth)
  await router.replace({ name: 'Login' })
}

</script>

<template>
  <main class="max-w-xl mx-auto p-6">
    <div class="rounded-xl border bg-white p-8 shadow">
      <h1 class="text-xl font-semibold mb-2">Compte en attente d’approbation</h1>

      <p class="text-gray-600">
        Votre compte n’est pas encore validé.
        C’est normal si vous vous êtes inscrit récemment&nbsp;: notre équipe va évaluer votre demande d’approbation
        et vous recontactera si des informations supplémentaires sont nécessaires.
        Si vous souhaitez nous écrire, utilisez le <router-link class="underline text-primary" :to="{ path: '/', hash: '#contact' }">formulaire de contact</router-link>.
      </p>

      <div class="mt-4 text-sm text-gray-500" v-if="email">
        <span class="font-medium">Adresse</span> : {{ email }}
        <span v-if="emailVerified === false" class="ml-2 text-amber-600">(email non vérifié)</span>
      </div>

      <div class="mt-6 flex flex-wrap gap-3">
        <button
          class="px-4 py-2 rounded-lg bg-gray-100 text-gray-800 font-medium hover:bg-gray-200"
          @click="doLogout"
        >
          Se déconnecter
        </button>
      </div>

      <p v-if="error" class="mt-4 text-red-600 text-sm">{{ error }}</p>

    </div>
  </main>
</template>
