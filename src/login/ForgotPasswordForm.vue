<template>
  <form @submit.prevent="onSubmit" class="space-y-4">
    <label class="block text-sm font-medium text-primary-dark mb-1">Email ou nom d’utilisateur</label>
    <input
      v-model="identifier"
      type="text"
      autocomplete="username"
      required
      class="w-full border border-gray-200 rounded-lg px-4 py-2 bg-bg focus:border-primary focus:ring-1 focus:ring-primary outline-none"
      :disabled="loading"
      placeholder="Email ou nom d’utilisateur"
    />

    <div v-if="error" class="text-red-600 text-sm text-center" role="alert" aria-live="assertive">{{ error }}</div>
    <div v-if="info" class="text-green-600 text-sm text-center" role="status" aria-live="polite">{{ info }}</div>

    <button
      :disabled="loading || !identifier"
      type="submit"
      class="w-full bg-cyan-600 hover:bg-cyan-700 text-white font-bold py-2.5 rounded-lg shadow transition disabled:opacity-70"
    >
      <span v-if="loading">Envoi…</span>
      <span v-else>Envoyer le lien de réinitialisation</span>
    </button>
  </form>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuth } from './composables/useAuth'

const props = defineProps<{ initialIdentifier?: string }>()
const emit = defineEmits<{ (e: 'reset-sent'): void }>()
const { sendPasswordReset, authErrorMessage } = useAuth()

const identifier = ref('')
const loading = ref(false)
const error = ref('')
const info = ref('')

onMounted(() => {
  if (props.initialIdentifier) identifier.value = props.initialIdentifier
})

async function onSubmit() {
  error.value = ''
  info.value = ''
  loading.value = true
  try {
    await sendPasswordReset(identifier.value)
    info.value = "Si un compte est associé à cet identifiant, un email de réinitialisation a été envoyé."
    emit('reset-sent')
  } catch (e: any) {
    error.value = authErrorMessage(e) || (e?.message || 'Une erreur est survenue.')
  } finally {
    loading.value = false
  }
}
</script>
