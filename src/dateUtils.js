// Utility: Pad number for formatting
const pad = n => n.toString().padStart(2, '0');

// Format as YYYY-MM-DD (date only)
export function toLocalDateString(date) {
  return [
    date.getFullYear(),
    pad(date.getMonth() + 1),
    pad(date.getDate())
  ].join('-');
}

// Format as YYYY-MM-DDTHH:mm (datetime, adjust as needed)
export function toLocalDateTimeString(date) {
  return [
    date.getFullYear(),
    pad(date.getMonth() + 1),
    pad(date.getDate())
  ].join('-') + 'T' + [pad(date.getHours()), pad(date.getMinutes())].join(':');
}

// Recursively convert Date objects to local strings (date only here)
export function stripDatesToLocalStrings(obj) {
  if (obj === null || obj === undefined) return obj;
  if (obj instanceof Date) {
    return toLocalDateTimeString(obj);
  }
  if (Array.isArray(obj)) {
    return obj.map(stripDatesToLocalStrings);
  }
  if (typeof obj === 'object') {
    const newObj = {};
    for (const k in obj) {
      newObj[k] = stripDatesToLocalStrings(obj[k]);
    }
    return newObj;
  }
  return obj;
}

export function formatDate(dateStr) {
  if (!dateStr) return 'N/A';
  const date = new Date(dateStr);
  return isNaN(date.getTime()) ? '-' : date.toLocaleDateString("fr", {day: "numeric", month: "short", year: "numeric"});
}

export function formatDateRange(
  startStr,
  endStr,
  locale = "fr",
  {
    dateFormat = "short",   // 'short', 'long', 'monthDay', or (date) => string
    timeFormat = "2-digit"  // '2-digit', 'none', { hour:..., minute:... } or (date) => string
  } = {}
) {
  const hasStart = !!startStr;
  const hasEnd = !!endStr;
  const startDate = hasStart ? new Date(startStr) : null;
  const endDate = hasEnd ? new Date(endStr) : null;
  const startValid = startDate && !isNaN(startDate.getTime());
  const endValid = endDate && !isNaN(endDate.getTime());
  if (!startValid && !endValid) return "N/A";

  // --- Date formatting ---
  function defaultDateFormat(date) {
    if (dateFormat === "monthDay") {
      return date.toLocaleDateString(locale, { day: "numeric", month: "long" });
    }
    if (dateFormat === "short") {
      return date.toLocaleDateString(locale);
    }
    if (dateFormat === "long") {
      return date.toLocaleDateString(locale, { year: "numeric", month: "long", day: "numeric" });
    }
    if (typeof dateFormat === "function") return dateFormat(date);
    // fallback
    return date.toLocaleDateString(locale);
  }

  // --- Time formatting ---
  function defaultTimeFormat(date) {
    if (timeFormat === "none") return "";
    if (timeFormat === "2-digit" || timeFormat === "short") {
      return date.toLocaleTimeString(locale, { hour: "2-digit", minute: "2-digit" });
    }
    if (typeof timeFormat === "object") {
      return date.toLocaleTimeString(locale, timeFormat);
    }
    if (typeof timeFormat === "function") return timeFormat(date);
    // fallback: no time
    return "";
  }

  // --- Use them! ---
  if (!startValid && endValid) {
    const endDatePart = defaultDateFormat(endDate);
    const endTimePart = defaultTimeFormat(endDate);
    return "..." + (endTimePart ? `${endDatePart} ${endTimePart}` : endDatePart);
  }
  if (startValid && !endValid) {
    const startDatePart = defaultDateFormat(startDate);
    const startTimePart = defaultTimeFormat(startDate);
    return (startTimePart ? `${startDatePart} ${startTimePart}` : startDatePart) + "...";
  }
  // both valid
  const startDatePart = defaultDateFormat(startDate);
  const startTimePart = defaultTimeFormat(startDate);
  const endDatePart = defaultDateFormat(endDate);
  const endTimePart = defaultTimeFormat(endDate);

  const sameDate = startDatePart === endDatePart;

  if (sameDate) {
    if (startTimePart || endTimePart) {
      const startTimeStr = startTimePart || "--:--";
      const endTimeStr = endTimePart || "--:--";
      return `${startDatePart} ${startTimeStr} - ${endTimeStr}`;
    }
    return startDatePart;
  } else {
    const startFull = startTimePart ? `${startDatePart} ${startTimePart}` : startDatePart;
    const endFull = endTimePart ? `${endDatePart} ${endTimePart}` : endDatePart;
    return `${startFull} - ${endFull}`;
  }
}
