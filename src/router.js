import { createRouter, createWebHistory } from 'vue-router'

// Import your components
import NotFoundView from './components/NotFoundView.vue'
import { getCurrentUser } from 'vuefire'
import AuthView from "./login/AuthView.vue"
import SitePageView from "./site/SitePageView.vue"
import GenericCoursesPage from "./site/GenericCoursesPage.vue";
import GenericCoursePage from "./site/GenericCoursePage.vue";
import AuthCompleteView from "./login/AuthCompleteView.vue";
import AuthAwaitingApproval from "./login/AuthAwaitingApproval.vue";
import MyInterventions from "./user/courses/MyInterventions.vue";
import MyCourses2 from "./user/courses/MyCourses2.vue";
import ResetPasswordView from "./login/ResetPasswordView.vue";
import MyActivities from "./user/courses/MyActivities.vue";
import LegalMentionsPage from "./site/LegalMentionsPage.vue";
import ConfidentialityNotesPage from "./site/ConfidentialityNotesPage.vue";
import CGVPage from "./site/CGVPage.vue";

// Define your application routes
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: AuthView
  },
  {
    path: '/auth/complete',
    name: 'auth-complete',
    component: AuthCompleteView,
    meta: { public: true }
  },

  {
    path: '/auth/attente-validation',
    name: 'auth-attente-validation',
    component: AuthAwaitingApproval,
    meta: { public: true }
  },
  {
    path: '/app/mes-activites',
    name: 'Activites',
    component: MyActivities,
    meta: { requiresAuth: true },
  },
  {
    path: '/app/mes-formations',
    name: 'Formations',
    component: MyCourses2,
    meta: { requiresAuth: true },
  },
  {
    path: '/app/mes-interventions',
    name: 'Interventions',
    component: MyInterventions,
    meta: { requiresAuth: true },
  },
  {
    path: '/',
    name: 'Accueil',
    component: SitePageView,
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFoundView,
  },
  {
  path: '/formations',
  name: 'CoursesPage',
  component: GenericCoursesPage,
  props: route => ({ initialFilter: route.query.filter || "" })
  },
  {
    path: '/formations/:id',
    name: 'CourseDetail',
    component: GenericCoursePage
  },
  {
    path: '/mentionslegales',
    name: 'LegalMentions',
    component: LegalMentionsPage,
  },
  {
    path: '/politiquesdeconfidentialite',
    name: 'PrivacyPolicy',
    component: ConfidentialityNotesPage,
  },
  {
    path: '/cgv',
    name: 'CGV',
    component: CGVPage,
  }
]

// Create the router instance, now with scrollBehavior for anchors
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // Return to saved scroll position on browser navigation
    if (savedPosition) return savedPosition
    // Scroll to anchor if hash is present
    if (to.hash) {
      return { el: to.hash, behavior: 'smooth' }
    }
    // Otherwise scroll to top
    return { top: 0 }
  }
})

router.beforeEach(async (to) => {
  // routes with `meta: { requiresAuth: true }` will check for
  // the users, others won't
  if (to.meta?.requiresAuth) {
    const currentUser = await getCurrentUser()
    // if the user is not logged in, redirect to the login page
    if (!currentUser) {
      return {
        path: '/login',
        query: {
          // we keep the current path in the query so we can
          // redirect to it after login with
          // `router.push(route.query.redirect || '/')`
          redirect: to.fullPath,
        },
      }
    }
  }
})

// Export the router so it can be used in the app
export default router
