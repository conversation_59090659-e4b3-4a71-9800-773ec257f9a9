import axios from 'axios';
import {API_BASE_URL} from "./config.js";


function createIdempotencyKey(prefix = 'req') {
  // Prefer crypto.randomUUID when available
  const uuid = (typeof crypto !== 'undefined' && crypto.randomUUID)
    ? crypto.randomUUID()
    : `r${Date.now()}-${Math.random().toString(36).slice(2)}${Math.random().toString(36).slice(2)}`
  return `${prefix}_${uuid}`
}

export async function postApi({
  endpoint_name,
  data = {},
  stream = false,
  returnOnlyData = true,
  response_type = null,
  contentType = null,
  axiosConfig = {},
  onError = null,
  idempotencyKey = null, // true | string | (payload)=>string | null
}) {
  const isFormData =
    (typeof FormData !== 'undefined') && (data instanceof FormData)

  // Always clone payload to avoid mutating caller's state
  const payload = isFormData ? data : { ...data }

  // Base headers
  const headers = isFormData
    ? {} // let Axios set multipart boundary
    : { 'Content-Type': contentType ?? 'application/json' }

  // Resolve idempotency key if requested
  let resolvedKey = null
  try {
    if (idempotencyKey === true) {
      resolvedKey = createIdempotencyKey('reg') // prefix is arbitrary
    } else if (typeof idempotencyKey === 'string' && idempotencyKey.trim()) {
      resolvedKey = idempotencyKey.trim()
    } else if (typeof idempotencyKey === 'function') {
      // Allow caller to derive a repeatable key from payload
      resolvedKey = idempotencyKey(payload)
    }
  } catch {
    // If generator throws, just skip adding the header
    resolvedKey = null
  }
  if (resolvedKey) {
    headers['Idempotency-Key'] = resolvedKey
  }

  const config = {
    headers: { ...headers, ...(axiosConfig.headers || {}) },
    ...axiosConfig,
  }

  if (stream) {
    config.responseType = 'stream'
    config.adapter = 'fetch'
  }
  if (response_type) {
    config.responseType = response_type
  }

  try {
    const response = await axios.post(
      `${API_BASE_URL}/${endpoint_name}`,
      payload,
      config
    )
    return returnOnlyData ? response.data : response
  } catch (error) {
    if (onError) onError(error)
    console.error(error)
    throw error
  }
}

export async function getApi({
  endpoint_name,
  queryParams: queryParams = {},
  stream = false,
  noneOk = false,
  contentType = null,
  throwError = false,
  showToast = true,
  axiosConfig = {},
  onError = null,
  returnOnlyData = true
}) {
  const config = {
    params: queryParams,
    paramsSerializer: { indexes: null },
    ...axiosConfig
  };

  if (stream) {
    config.responseType = "stream";
    config.adapter = "fetch";
  }
  if (contentType) {
    config.responseType = contentType;
  }

  try {
    const response = await axios.get(
      `${API_BASE_URL}/` + endpoint_name,
      config,
    );
    return returnOnlyData ? response.data : response;
  } catch (error) {
    if (onError) onError(error);
    console.error(error);
    throw error;
  }
}

export async function deleteApi({
  endpoint_name,
  queryParams = {},
  axiosConfig = {},
  onError = null,
}) {
  const config = {
    params: queryParams,
    paramsSerializer: { indexes: null },
    ...axiosConfig
  };
  try {
    const response = await axios.delete(
      `${API_BASE_URL}/` + endpoint_name,
      config,
    );
    return response.data;
  } catch (error) {
    if (onError) onError(error);
    console.error(error);
    throw error;
  }
}

export async function getUserCourses() {
    return await getApi({
        endpoint_name:"user/courses"
    });
}

export async function getUserInterventions() {
    return await getApi({
        endpoint_name:"user/interventions"
    });
}

export async function getUserQuiz(sessionSectionId) {
    return await getApi({
        endpoint_name:`user/get_user_quiz`,
        queryParams: {
            session_section_id: sessionSectionId
        }
    });
}

export async function initUserQuizTimer(quizId, sessionSectionId) {
    return await postApi({
        endpoint_name:`user/init_user_quiz_timer`,
        data: {
            quiz_id: quizId,
            session_section_id: sessionSectionId
        }
    });
}

export async function updateUserQuizTimer(trackerId) {
    return await postApi({
        endpoint_name:`user/update_user_quiz_timer`,
        data: {
            tracker_id: trackerId
        }
    });
}

export async function saveUserQuizAnswers(answers) {
    return await postApi({
        endpoint_name:`user/save_user_quiz_answers`,
        data: {
            answers: answers
        }
    });
}

export async function getUpcomingCourses(id=null, details=false) {
    return await getApi({
        endpoint_name:`muser/get_upcoming_courses`,
        queryParams: {
            id, details
        }
    });
}

export async function downloadStageImage(filename) {
    return await getApi({
        endpoint_name: 'muser/download/stage-image',
        queryParams: {
            filename
        },
        contentType: 'blob'
    })
}

export async function downloadDocument(id) {
    return await getApi({
        endpoint_name: 'user/download_document',
        queryParams: {
            id
        },
        contentType: 'blob'
    })
}

export async function downloadQuizImage(name) {
    return await getApi({
        endpoint_name: 'user/download_quiz_image',
        queryParams: {
            filename: name
        },
        contentType: 'blob'
    })
}


export async function uc(email, password) {
    return await postApi({
        endpoint_name: 'muser/uc',
        data: {
            identifier: email, password: password
        }
    })
}

export async function uca() {
    return await postApi({
        endpoint_name: 'muser/uca',
        data: {}
    })
}


export async function register(payload) {
    return await postApi({
        endpoint_name: 'muser/register',
        data: payload,
        returnOnlyData: false
    })
}


export async function sendContact(payload) {
  // bot trap: if honeypot is filled, silently no-op
  if (payload && payload.hp && payload.hp.trim()) {
    return { ok: true }
  }

  const data = {
    name: (payload.name || '').trim(),
    prenom: (payload.prenom || '').trim(),
    tel: (payload.tel || '').trim(),
    email: (payload.email || '').trim(),
    message: (payload.message || '').trim()
  }

  return await postApi({
    endpoint_name: 'muser/contact',           // => POST ${API_BASE_URL}/contact
    data,
    contentType: 'application/json',
    // axiosConfig: { timeout: 12000 },  // optional
    onError: (err) => {
      // place to log to Sentry etc.
    }
  })
}

export async function registerToCourse(courseId) {
  return await postApi({
    endpoint_name: 'user/register_to_course',
    data: {
      session_id: courseId,
    }
  })
}

export async function cancelCourseRegistration(courseId) {
  return await postApi({
    endpoint_name: 'user/cancel_course_registration',
    data: {
      session_id: courseId,
    }
  })
}

export async function getTime() {
  return await getApi({
    endpoint_name: 'muser/tnp'
  })
}

export async function getUserQuizForCorrection(inscriptionId, sessionSectionId) {
    return await getApi({
        endpoint_name: "user/get_user_quiz_for_correction",
        queryParams: {inscription_id: inscriptionId, session_section_id: sessionSectionId}
    })
}

export async function getQuizStats(sessionSectionId) {
    return await getApi({
        endpoint_name: "user/get_quiz_stats",
        queryParams: {session_section_id: sessionSectionId}
    })
}

export async function saveUserQuizGrading(session_section_id, answers, manualCompletionData) {
    return await postApi({
        endpoint_name: 'user/save_user_quiz_grading',
        data: {session_section_id: session_section_id, answers: answers, manual_completion_data: manualCompletionData}
    })
}

export async function getParticipantsForQuizCorrection(sessionSectionId) {
    return await getApi({
        endpoint_name: "user/get_participants_for_quiz_correction",
        queryParams: {session_section_id: sessionSectionId}
    })
}

export async function gradeQuizAnswerWithAI(sessionSectionId, question, correctAnswer, userAnswer) {
    return await postApi({
        endpoint_name: 'user/grade_quiz_answer_with_ai',
        data: {session_section_id: sessionSectionId, question, correct_answer: correctAnswer, user_answer: userAnswer}
    })
}