<script setup>
import {ref, onMounted, computed, watch, onBeforeUnmount, onActivated, onDeactivated, toRefs} from "vue";

import {formatDateRange} from "../dateUtils.js";
import {getUserQuiz, initUserQuizTimer, saveUserQuizAnswers, updateUserQuizTimer} from "../services.js";
import QuizTakerBlock from "./QuizTakerBlock.vue";
import AisyngChip from "../modules/chips/AisyngChips.vue";
import AisyngButton from "../modules/button/AisyngButton.vue";

const props = defineProps({
  sessionSectionId: [String, Number],
  inscriptionId: [String, Number],
});
const {sessionSectionId, inscriptionId} = toRefs(props);

const emit = defineEmits(["quizSaved"]);

const data = ref(null);
const responsesFull = ref({});
const quizStarted = ref(false);
const quizCompleted = ref(false);
const timeTracker = ref(null);

const elapsedSeconds = ref(0);
let timerInterval = null;
let trackerUpdateInterval = null;

// ---- Lifecycle safety ----
let isAlive = true; // prevents “resurrecting” timers after close

function cleanup() {
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value);
    saveTimeout.value = null;
  }
  stopTimer();
  stopTrackerUpdate();
}

onBeforeUnmount(() => {
  isAlive = false;
  cleanup();
});

onDeactivated(() => {
  isAlive = false;
  cleanup();
});

onActivated(() => {
  isAlive = true;
  // Resume timers only when appropriate
  if (!lockQuiz.value) {
    startTimer();
    startTrackerUpdate();
  }
});

// ---- Error handling ----
const errorMsg = ref(""); // Native error handling

function showError(message) {
  errorMsg.value = message;
  setTimeout(() => (errorMsg.value = ""), 7000);
}

// ---- Periodic backend timer update ----
function startTrackerUpdate() {
  if (!isAlive || trackerUpdateInterval) return;
  trackerUpdateInterval = setInterval(updateQuizTimer, 60000);
}

function stopTrackerUpdate() {
  if (trackerUpdateInterval) {
    clearInterval(trackerUpdateInterval);
    trackerUpdateInterval = null;
  }
}

async function updateQuizTimer() {
  try {
    if (!isAlive || !data.value || !timeTracker.value) return;
      const res = await updateUserQuizTimer(timeTracker.value.quiz_connection_id);
     // If the backend started a new segment, its tracker id may change
     if (res?.quiz_connection_id && timeTracker.value.quiz_connection_id !== res.quiz_connection_id) {
       timeTracker.value.quiz_connection_id = res.quiz_connection_id;
     }
     // Use server-authoritative total time to correct local counters
     if (typeof res?.total_seconds === "number" && res.total_seconds >= 0) {
       const totalSecs = Math.floor(res.total_seconds);
       const newPrevMin = Math.floor(totalSecs / 60);
       const newRemainder = totalSecs - newPrevMin * 60;
       // update server minutes
       if (data.value) {
         data.value = {
           ...data.value,
           evaluation: { ...(data.value.evaluation || {}), conn_minutes: newPrevMin },
         };
       }
       // keep the remainder in the current session counter
       elapsedSeconds.value = newRemainder;
     }
  } catch (error) {
    // No blocking; just warn
    console.warn("Failed to update quiz timer:", error);
  }
}

// ---- Timer display and limits ----
const timerDisplay = computed(() => {
  const mm = String(Math.floor(elapsedSeconds.value / 60)).padStart(2, "0");
  const ss = String(elapsedSeconds.value % 60).padStart(2, "0");
  return `${mm}:${ss}`;
});
const timeLimitSeconds = computed(() =>
    data.value?.quiz_head?.min_minutes ? data.value.quiz_head.min_minutes * 60 : null
);

// include previously spent seconds in the countdown
const previousMinutes = computed(() => parseInt(data.value?.evaluation?.conn_minutes || 0));
const previousSeconds = computed(() => previousMinutes.value * 60);
const totalElapsedSeconds = computed(() => previousSeconds.value + elapsedSeconds.value);
const timeLeft = computed(() =>
    timeLimitSeconds.value !== null ? Math.max(0, timeLimitSeconds.value - totalElapsedSeconds.value) : null
);

const timeUp = computed(() => timeLeft.value === 0);
const minMinutes = computed(() => parseInt(data.value?.quiz_head?.min_minutes || 0));
const currentMinutes = computed(() => Math.floor(elapsedSeconds.value / 60));
const totalMinutes = computed(() => previousMinutes.value + currentMinutes.value);
const minutesRemaining = computed(() => Math.max(0, minMinutes.value - totalMinutes.value));
const minMet = computed(() => totalMinutes.value >= minMinutes.value);

// minutes allowed in total = min_minutes + 20
const hardMaxMinutes = computed(() => (minMinutes.value || 0) + 800);
const hardMaxSeconds = computed(() => hardMaxMinutes.value * 60);

// true when user has spent >= hard cap
const timeUpHard = computed(() => totalElapsedSeconds.value >= hardMaxSeconds.value);

const lockQuiz = computed(() => quizCompleted.value || timeUpHard.value);

watch(timeUpHard, (up) => {
  if (up) {
    stopTimer();
    stopTrackerUpdate();
    handleAutoSubmit();
  }
});

// ---- Timer control ----
function startTimer() {
  if (!isAlive || lockQuiz.value) return;
  if (timerInterval) return;
  quizStarted.value = true;
  timerInterval = setInterval(() => {
    elapsedSeconds.value++;
  }, 1000);
}

function stopTimer() {
  if (timerInterval) {
    clearInterval(timerInterval);
    timerInterval = null;
  }
}

// ---- Load and prepare data ----
async function loadData(blocking = false) {
  try {
    if (sessionSectionId.value && inscriptionId.value) {
      data.value = await getUserQuiz(sessionSectionId.value);
      const initialResponses = data.value.answers[data.value.user.id] || {};
      const mergedResponses = {};
      let needsInitialSave = false;

      function processBlock(block) {
        if (block.type !== "content" && block.qa?.length) {
          block.qa.forEach((option) => {
            const id = option.id;
            const existing = initialResponses[id];
            if (existing) {
              mergedResponses[id] = {...existing};
            } else {
              mergedResponses[id] = defaultResponse(id, block.id);
              needsInitialSave = true;
            }
          });
        }
      }

      function defaultResponse(answerId, questionId) {
        return {
          id: null,
          utilisateur_id: parseInt(data.value.user.id),
          answer_id: answerId,
          question_id: questionId,
          session_section_id: data.value.section.session_section_id,
          inscription_id: data.value.inscription.id,
          quiz_id: data.value.quiz_head.id,
          response_text: "",
          response_check: 0,
          response_value: null,
          date: new Date(),
        };
      }

      data.value.blocks.forEach((block) => processBlock(block));
      responsesFull.value = mergedResponses;

      // Prefill all missing records and reload for IDs, only once!
      if (needsInitialSave) {
        await saveUserQuizAnswers(responsesFull.value);
        data.value = await getUserQuiz(sessionSectionId.value);
        const loadedResponses = data.value.answers[data.value.user.id] || {};
        const mergedResponsesWithIDs = {};
        data.value.blocks.forEach((block) => {
          if (block.type !== "content" && block.qa?.length) {
            block.qa.forEach((option) => {
              const id = option.id;
              mergedResponsesWithIDs[id] = loadedResponses[id]
                  ? {...loadedResponses[id]}
                  : defaultResponse(id, block.id);
            });
          }
        });
        responsesFull.value = mergedResponsesWithIDs;
      }
    }
  } finally {
    // Prevent “resurrection” if component closed/deactivated while awaiting
    if (!isAlive) return;
    elapsedSeconds.value = 0;
    startTimer();
  }
}

// ---- Autosave logic ----
const saveTimeout = ref(null);
const isSaving = ref(false);
const lastSavedAt = ref(null);

function onUserInput() {
  if (saveTimeout.value) return;
  saveTimeout.value = setTimeout(async () => {
    await autosave();
    saveTimeout.value = null;
  }, 10000); // 10s
}

async function autosave() {
  if (isSaving.value || quizCompleted.value) return;
  isSaving.value = true;
  try {
    await saveUserQuizAnswers(responsesFull.value);
    lastSavedAt.value = new Date();
    await updateQuizTimer();
  } catch (error) {
    showError(
        "Un problème est survenu lors de la sauvegarde du questionnaire. Si le problème persiste, veuillez nous contacter."
    );
  } finally {
    isSaving.value = false;
  }
}

async function handleSubmit() {
  if (saveTimeout.value) {
    clearTimeout(saveTimeout.value);
    saveTimeout.value = null;
  }
  isSaving.value = true;
  try {
    const saveResult = await saveUserQuizAnswers(responsesFull.value);
    if (saveResult === "error") throw new Error("Erreur de sauvegarde");
    const timerResult = await updateQuizTimer();
    if (timerResult === "error") throw new Error("Erreur de minuteur");

    lastSavedAt.value = new Date();
    quizCompleted.value = true;

    // Ensure everything stops on completion
    stopTimer();
    stopTrackerUpdate();

    emit("quizSaved");
  } catch (error) {
    showError(
        "Un problème est survenu lors de la sauvegarde du questionnaire. Si le problème persiste, veuillez nous contacter."
    );
  } finally {
    isSaving.value = false;
  }
}

async function initQuizTimer() {
  try {
    timeTracker.value = await initUserQuizTimer(
        data.value.inscription.utilisateur_id,
        data.value.section.session_section_id,
        data.value.section.quiz_id
    );
  } catch (error) {
    showError("Il y a un problème avec le minuteur du questionnaire, votre temps ne sera pas suivi correctement.");
  }
}

async function handleAutoSubmit() {
  await handleSubmit();
}

// ---- Main lifecycle ----
onMounted(async () => {
  await loadData(true);
  await initQuizTimer();
  startTrackerUpdate();
});
</script>

<template>
  <div v-if="data" class="max-w-7xl mx-auto px-2 sm:px-6 pb-12">
    <!-- Quiz Header -->
    <div class="bg-gray-50 border border-gray-200 rounded-2xl px-6 py-5 mb-10 shadow sticky top-0 z-10">
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-8 mb-3">

        <!-- LEFT: Title + interval -->
        <div class="flex-1 text-center md:text-left">
          <div class="text-xl md:text-xl font-bold mb-3 text-[var(--color-amf-primary)]">
            {{ data.section.name }}
          </div>
          <AisyngChip size="md" variant="soft" color="teal">
            🗓️ {{ formatDateRange(data.section.start_time, data.section.end_time) }}
          </AisyngChip>
        </div>

        <!-- MIDDLE: stacked chips -->
        <div class="flex flex-col items-center gap-2">
          <AisyngChip v-if="timeLimitSeconds" size="md" variant="soft" color="teal">
            ⏳ <b>{{ minMinutes }}</b> minutes requis pour ce quiz
          </AisyngChip>
          <AisyngChip v-if="timeLimitSeconds" size="md" variant="soft" color="blue">
            ⏳ <b>{{ totalMinutes }}</b> minutes deja passées
          </AisyngChip>
          <AisyngChip v-if="timeLimitSeconds" size="md" variant="soft"
                      :color="minutesRemaining <= 0 ? 'green' : 'red'">
            <span v-if="minutesRemaining > 0">
              ⏳ <b>{{ minutesRemaining }}</b> minutes à faire
            </span>
            <span v-else>
              ⏳ <b>Temps écoulé</b>
            </span>
          </AisyngChip>
        </div>

        <!-- RIGHT: Button -->
        <div class="flex-1 flex flex-col justify-center md:justify-end">
          <AisyngButton
              v-if="!quizCompleted"
              variant="normal"
              :disabled="lockQuiz || isSaving"
              :loading="isSaving"
              @click="handleSubmit"
          >
            💾 Envoyer mes réponses
          </AisyngButton>
          <!-- Error + Last saved -->
          <div v-if="errorMsg"
               class="mt-3 p-2 rounded bg-red-100 text-red-800 text-sm text-center font-semibold shadow">
            {{ errorMsg }}
          </div>
          <div v-if="lastSavedAt" class="mt-1 text-xs text-gray-500 text-center">
            Sauvegardé à {{ lastSavedAt.toLocaleTimeString('fr-FR') }}
          </div>
        </div>
      </div>
    </div>

    <!-- Quiz Blocks -->
    <div>
      <div v-for="block in data.blocks" :key="block.id">
        <QuizTakerBlock
            :block="block"
            :responses="responsesFull"
            @input="onUserInput"
            :readonly="lockQuiz"
        />
      </div>
    </div>

    <div
        v-if="quizCompleted"
        class="mt-8 p-6 rounded-xl bg-green-50 text-green-900 text-lg font-semibold text-center border border-green-100 shadow"
    >
      Merci, vos réponses ont été envoyées !
    </div>
  </div>
  <div v-else>
    <div class="text-lg text-center py-12 text-gray-600">Chargement du questionnaire…</div>
  </div>
</template>
