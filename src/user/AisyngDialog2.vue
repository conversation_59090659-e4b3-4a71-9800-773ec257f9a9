
<script setup>
import { ref, defineEmits, onMounted, onBeforeUnmount } from 'vue'
const emit = defineEmits(['close'])

const props = defineProps({
  title: { type: String, default: ''},
  showTitleBar: {type: Boolean, default: true}
})

const isVisible = ref(false)

function open() {
  isVisible.value = true
  setTimeout(() => {
    try {
      document.querySelector('.dialog-content')?.focus()
    } catch (e) {
    }
  }, 10)
}

function close() {
  isVisible.value = false
  emit('close')
}

function handleEsc(e) {
  if (isVisible.value && e.key === 'Escape') {
    close()
  }
}

onMounted(() => {
  window.addEventListener('keydown', handleEsc)
})

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleEsc)
})

defineExpose({open, close})
</script>

<template>
  <transition name="dialog-fade">
    <div v-if="isVisible" class="dialog-backdrop" @click.self="close">
      <div class="dialog-content flex flex-col" @keydown.esc="close" tabindex="0">
        <div
          v-if="showTitleBar"
          class="dialog-title-bar sticky top-0 z-10 flex items-center justify-between px-5 py-3"
          :style="{
            borderTopLeftRadius: '8px',
            borderTopRightRadius: '8px',
            background: 'linear-gradient(90deg, #0d9488 0%, #14b8a6 100%)',
          }"
        >
          <div class="flex items-center gap-2">
            <slot name="icon"></slot>
            <span class="dialog-title text-white text-lg font-semibold select-none">
              <slot name="title">{{ title }}</slot>
            </span>
          </div>
          <button
            @click="close"
            class="dialog-close-btn text-white hover:text-blue-200 text-2xl leading-none rounded-full p-1 transition"
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg"
                 viewBox="0 0 24 24" width="24" height="24"
                 aria-hidden="true" focusable="false">
              <path d="M6 6L18 18M6 18L18 6"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        <div class="dialog-scrollable flex-1 overflow-auto">
          <slot />
        </div>
      </div>
    </div>
  </transition>
</template>

<style scoped>
.dialog-content {
  background-color: white;
  margin: 5rem;
  border-radius: 8px;
  max-width: 100%;
  width: 90vw;
  max-height: 90vh; /* Important for scroll! */
  min-height: 100px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 999001;
  animation: popInContent 0.3s ease-out;
  outline: none;
}
.dialog-scrollable {
  flex: 1 1 0%;
  overflow: auto;
  /* Padding can go here or in content for layout */
}
.dialog-title-bar {
  min-height: 2.5rem;
  user-select: none;
  position: sticky;
  top: 0;
  z-index: 10;
  /* The background is set inline, but you can move it here if preferred */
}

.dialog-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999000;
  animation: fadeInBackdrop 0.3s ease-out;
}


.dialog-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
}

@keyframes fadeInBackdrop {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes popInContent {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Fade for dialog in and out */
.dialog-fade-enter-active, .dialog-fade-leave-active {
  transition: opacity 0.25s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.dialog-fade-enter-from, .dialog-fade-leave-to {
  opacity: 0;
}

.dialog-fade-enter-to, .dialog-fade-leave-from {
  opacity: 1;
}
</style>
