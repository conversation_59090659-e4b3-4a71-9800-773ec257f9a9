<script setup lang="ts">
import SectionItemIntervention from "./SectionItemIntervention.vue";
import type { Course, Section } from "./useCoursesLogic";

defineProps<{ sections: Section[]; course: Course }>();
const emit = defineEmits<{ (e: "open-grading", payload: { section: Section; course: Course }): void }>();
</script>

<template>
  <div v-if="sections && sections.length">
    <div class="relative">
    </div>
  </div>
      <div class="absolute left-3 top-0 bottom-0 w-px bg-gray-200" />
      <div class="space-y-3">
        <div v-for="section in sections" :key="section.session_section_id" class="pl-8">
          <SectionItemIntervention :section="section" :course="course" @open-grading="p => emit('open-grading', p)" />
        </div>
      </div>
</template>
