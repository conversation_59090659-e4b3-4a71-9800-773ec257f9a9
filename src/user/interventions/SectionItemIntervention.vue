<script setup lang="ts">
import { computed } from "vue";
import {
  canJoinZoom,
  joinAvailabilityText,
} from "../courses/useCoursesLogic";

import { isTodayParis, formatDateRange } from "../../modules/clock/clock";
import { getGradingStats } from "../courses/useInterventionsLogic";
import type { Course, Section } from "../courses/useCoursesLogic";

const props = defineProps<{ section: Section; course: Course }>();
const emit = defineEmits<{ (e: "open-grading", payload: { section: Section; course: Course }): void }>();

// keep reactive if the section can update
const stats = computed(() => getGradingStats(props.section));
</script>

<template>
  <div class="flex items-start gap-3">
    <!-- Timeline dot -->
    <span
      class="absolute -ml-[22px] mt-2 inline-block w-3 h-3 rounded-full border"
      :class="{
        'bg-amber-400 border-amber-500': isTodayParis(section.start_time),
        'bg-blue-500 border-blue-600': !isTodayParis(section.start_time),
      }"
    />

    <div class="flex-1 bg-gray-50 border border-gray-200 rounded-lg p-3">
      <!-- 3-column grid -->
      <div class="grid grid-cols-1 md:grid-cols-12 items-center gap-4">
        <!-- Col 1: type + name + date -->
        <div class="md:col-span-6 min-w-0">
          <div class="flex flex-wrap items-center gap-2">
            <span
              class="text-[11px] font-semibold uppercase tracking-wide px-2 py-0.5 rounded"
              :class="section.type === 'FORMULAIRE' ? 'bg-sky-100 text-sky-700' : 'bg-indigo-100 text-indigo-700'"
            >
              {{ section.type === 'FORMULAIRE' ? 'Formulaire' : 'Présentation' }}
            </span>
            <strong class="truncate">{{ section.name }}</strong>
          </div>
          <div class="text-sm text-gray-700 mt-1 flex items-center gap-2">
            <span class="whitespace-nowrap">📅 {{ formatDateRange(section.start_time, section.end_time) }}</span>
            <span
              v-if="isTodayParis(section.start_time)"
              class="px-2 py-0.5 rounded text-[11px] font-bold bg-amber-200 text-gray-900"
            >
              Aujourd'hui
            </span>
          </div>
        </div>

        <!-- Col 2: quiz statistics (only for FORMULAIRE) -->
        <div class="md:col-span-4">
          <div v-if="section.type === 'FORMULAIRE'" class="text-sm text-gray-800">
            <div class="flex flex-col gap-x-6 gap-y-1">
              <div>
                <span class="font-bold">Complétés: </span>
                <strong>{{ stats.finishedQuizzes }}</strong>/<span class="text-gray-500">{{ stats.totalQuizzes }}</span>
              </div>
              <div>
                <span class="font-bold">Corrigés (participants): </span>
                <strong>{{ stats.gradedQuizzes }}</strong>/<span class="text-gray-500">{{ stats.finishedQuizzes }}</span>
              </div>
              <div>
                <span class="font-bold">Questions corrigées: </span>
                <strong>{{ stats.totalGradedQuestions }}</strong>/<span class="text-gray-500">{{ stats.totalAnsweredQuestions }}</span>
              </div>
            </div>
          </div>
          <div v-else class="text-sm text-gray-400">&nbsp;</div>
        </div>

        <!-- Col 3: action (square button), right-aligned -->
        <div class="md:col-span-2 justify-self-end">
          <!-- Join Zoom (Presentation) -->
          <div
            v-if="section.type === 'PRESENTATION' && section.zoom_url"
            class="flex flex-col items-center w-24"
          >
            <a
              :href="canJoinZoom(section) ? section.zoom_url : '#'"
              :target="canJoinZoom(section) ? '_blank' : undefined"
              :rel="canJoinZoom(section) ? 'noopener' : undefined"
              class="inline-flex items-center justify-center w-16 h-16 rounded-xl transition
                     focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300"
              :class="canJoinZoom(section)
                        ? 'hover:ring-4 hover:ring-indigo-300 bg-white'
                        : 'opacity-40 cursor-not-allowed bg-gray-100 pointer-events-none'"
              :aria-disabled="!canJoinZoom(section)"
              :title="canJoinZoom(section) ? 'Rejoindre la session Zoom' : joinAvailabilityText(section)"
              @click.prevent="!canJoinZoom(section)"
            >
              <img src="/images/online_join_icon.svg" alt="Zoom" class="w-10 h-10" />
            </a>
            <span class="text-xs mt-1">Rejoindre</span>
            <span v-if="!canJoinZoom(section)" class="text-[11px] text-gray-500 mt-0.5">
              {{ joinAvailabilityText(section) }}
            </span>
          </div>

          <!-- Correct (Form) -->
          <div v-else-if="section.type === 'FORMULAIRE'" class="flex flex-col items-center w-24">
            <button
              class="inline-flex items-center justify-center w-16 h-16 rounded-xl transition
                     focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-300
                     hover:ring-4 hover:ring-orange-300 bg-white"
              @click="emit('open-grading', { section, course })"
              title="Corriger les quiz"
            >
              <img src="/images/take_quiz_icon.svg" alt="Corriger" class="w-10 h-10" />
            </button>
            <span class="text-xs mt-1">Corriger</span>
          </div>

          <!-- No action (safety fallback) -->
          <div v-else class="text-sm text-gray-500">Aucune action</div>
        </div>
      </div>
    </div>
  </div>
</template>
