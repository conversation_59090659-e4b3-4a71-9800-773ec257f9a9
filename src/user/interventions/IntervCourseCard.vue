<script setup lang="ts">
import { ref, computed } from "vue";
import CourseProgressBar from "../courses/CourseProgressBar.vue";
import SectionTimelineIntervention from "./SectionTimelineIntervention.vue";
import {
  formatTimeHHMM,
  formatDateRange,
  isSameDayParis,
} from "../../modules/clock/clock";
import {
  canJoinZoom,
  joinAvailabilityText
} from "../courses/useCoursesLogic";
import type { Course } from "../courses/useCoursesLogic";
import type { IntervNextAction } from "../courses/useInterventionsLogic";
import SectionItem from "../courses/SectionItem.vue";

const props = defineProps<{
  course: Course & { _progress?: { completed: number; total: number; ratio: number }; _next?: IntervNextAction | null };
}>();

const emit = defineEmits<{
  (e: "open-grading", payload: { section: any; course: Course }): void;
}>();

// --- Collapse sections (par défaut replié) ---
const sectionsOpen = ref(false);
const sectionsCount = computed(() =>
  Array.isArray(props.course.sections) ? props.course.sections.length : 0
);
const contentId = computed(() => `interv-sections-${props.course.session_id}`);

// --- Chip d’intervalle de dates (cours) ---
const courseDateChip = computed(() => {
  const s = props.course.start_date;
  const e = props.course.end_date;
  if (!s && !e) return "";
  const fmt = new Intl.DateTimeFormat("fr-FR", {
    timeZone: "Europe/Paris",
    day: "2-digit",
    month: "short",
    year: "numeric",
  });
  if (s && e) {
    const sd = new Date(s);
    const ed = new Date(e);
    return isSameDayParis(sd, ed) ? fmt.format(sd) : `${fmt.format(sd)} — ${fmt.format(ed)}`;
  }
  if (s) return fmt.format(new Date(s));
  return `jusqu’au ${fmt.format(new Date(e!))}`;
});

// --- Transition lisse sans “saut” ---
const setTransition = (e: HTMLElement) => {
  e.style.transition = "height 280ms cubic-bezier(.2,.7,.2,1), opacity 200ms ease";
  e.style.willChange = "height, opacity";
};
const clearTransition = (e: HTMLElement) => {
  e.style.transition = "";
  e.style.willChange = "";
};
const onBeforeEnter = (el: Element) => {
  const e = el as HTMLElement;
  e.style.overflow = "hidden";
  e.style.height = "0px";
  e.style.opacity = "0";
};
const onEnter = (el: Element) => {
  const e = el as HTMLElement;
  void e.offsetHeight;           // force reflow
  setTransition(e);
  e.style.height = e.scrollHeight + "px";
  e.style.opacity = "1";
};
const onAfterEnter = (el: Element) => {
  const e = el as HTMLElement;
  e.style.height = "auto";
  e.style.overflow = "";
  clearTransition(e);
};
const onBeforeLeave = (el: Element) => {
  const e = el as HTMLElement;
  e.style.overflow = "hidden";
  e.style.height = e.scrollHeight + "px";
  e.style.opacity = "1";
  void e.offsetHeight;           // reflow
};
const onLeave = (el: Element) => {
  const e = el as HTMLElement;
  setTransition(e);
  e.style.height = "0px";
  e.style.opacity = "0";
};
const onAfterLeave = (el: Element) => {
  const e = el as HTMLElement;
  e.style.overflow = "";
  clearTransition(e);
};
</script>

<template>
  <div class="bg-white rounded-2xl shadow p-6 hover:shadow-lg transition">
    <!-- Header (image + main + right slot) -->
    <div class="flex flex-col md:flex-row gap-6">
      <!-- Image -->
      <div class="flex-shrink-0 w-full md:w-40 h-32">
        <img
          v-if="(course as any).cover"
          :src="(course as any).cover"
          :alt="course.course_head?.libellestage || course.title || 'Formation'"
          class="rounded-xl shadow-md w-full h-full object-cover"
        />
        <img
          v-else
          src="/images/course_image.jpg"
          :alt="course.course_head?.libellestage || course.title || 'Formation'"
          class="rounded-xl shadow-md w-full h-full object-cover"
        />
      </div>

      <!-- Main -->
      <div class="flex-1 min-w-0">
        <div class="flex flex-wrap items-center gap-3 mb-2">
          <h2 class="text-xl font-semibold text-gray-900 truncate">
            {{ course.course_head?.libellestage || course.title }}
          </h2>
          <span
            class="px-2 py-1 rounded text-xs font-medium"
            :class="{
              'bg-green-100 text-green-700': course.temporalStatus === 'done',
              'bg-blue-100 text-blue-700': course.temporalStatus === 'in-progress',
              'bg-gray-200 text-gray-700': course.temporalStatus === 'upcoming',
            }"
          >
            {{
              course.temporalStatus === 'done' ? 'Terminé' : course.temporalStatus === 'in-progress' ? 'En cours' : 'À venir'
            }}
          </span>
          <span class="px-2 py-1 rounded text-xs font-medium bg-rose-100 text-rose-700">
            {{ course.intervention?.nature || 'Intervenant' }}
          </span>

          <!-- Chip d’intervalle de dates -->
          <span
            v-if="courseDateChip"
            class="inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700"
            :title="courseDateChip"
          >
            <span aria-hidden="true">📆</span>
            {{ courseDateChip }}
          </span>
        </div>

        <p v-if="course.desc" class="text-gray-600 mb-3">
          {{ course.desc }}
        </p>

        <!-- Progress -->
        <CourseProgressBar
          v-if="course._progress && course._progress.total > 0"
          :completed="course._progress.completed"
          :total="course._progress.total"
          :percent="course._progress.ratio"
          class="mb-3"
        />
      </div>

      <!-- Right column slot (venue, etc.) -->
      <div class="hidden md:block md:w-64">
        <slot name="right" :course="course" />
      </div>
    </div>

    <!-- Collapsible: Sections timeline (animated element without external margins) -->
    <div class="mt-0">
      <Transition
        @before-enter="onBeforeEnter"
        @enter="onEnter"
        @after-enter="onAfterEnter"
        @before-leave="onBeforeLeave"
        @leave="onLeave"
        @after-leave="onAfterLeave"
      >
        <div v-show="sectionsOpen" :id="contentId">
          <SectionItem
            :sections="course.sections || []"
            :course="course"
            @open-grading="p => $emit('open-grading', p)"
          />
        </div>
      </Transition>
    </div>
  </div>
</template>
