<template>
  <transition name="dialog-fade">
    <div
      v-if="isVisible"
      class="dialog-backdrop"
      @click.self="close"
      tabindex="-1"
      ref="backdrop"
    >
      <transition name="dialog-pop">
        <div class="dialog-content" ref="content" @keydown.esc="close" tabindex="0">
          <button
            class="dialog-close"
            type="button"
            @click="close"
            aria-label="Fermer"
          >&times;</button>
          <slot />
        </div>
      </transition>
    </div>
  </transition>
</template>

<script setup>
import { ref, defineEmits, nextTick, watch } from 'vue'
const emit = defineEmits(['close'])
const isVisible = ref(false)

function open() {
  isVisible.value = true
  nextTick(() => {
    // Focus the dialog for keyboard accessibility
    content.value?.focus()
  })
}
function close() {
  isVisible.value = false
  emit('close')
}

const backdrop = ref(null)
const content = ref(null)

// Trap focus when dialog is open
function trapFocus(e) {
  if (!isVisible.value) return
  if (content.value && !content.value.contains(document.activeElement)) {
    content.value.focus()
  }
}
watch(isVisible, v => {
  if (v) {
    document.addEventListener('focusin', trapFocus)
  } else {
    document.removeEventListener('focusin', trapFocus)
  }
})

defineExpose({ open, close })
</script>

<style scoped>
.dialog-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(30, 41, 59, 0.55); /* blue-black */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999000;
  animation: fadeInBackdrop 0.3s cubic-bezier(.4,0,.2,1);
}

.dialog-content {
  background: #fff;
  position: relative;
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  border-radius: 1.1rem;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 6px 32px #0003, 0 2px 8px #0001;
  outline: none;
  animation: popInContent 0.25s cubic-bezier(.4,0,.2,1);
}

.dialog-close {
  position: absolute;
  top: 1rem;
  right: 1.1rem;
  background: none;
  border: none;
  font-size: 2.1rem;
  color: #64748b;
  cursor: pointer;
  padding: 0;
  z-index: 2;
  line-height: 1;
  transition: color .15s;
}
.dialog-close:hover {
  color: #0ea5e9;
  background: #f1f5f9;
  border-radius: 50%;
}

@keyframes fadeInBackdrop {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes popInContent {
  from {
    opacity: 0;
    transform: scale(0.98) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* For <transition> */
.dialog-fade-enter-active, .dialog-fade-leave-active {
  transition: opacity 0.2s;
}
.dialog-fade-enter-from, .dialog-fade-leave-to {
  opacity: 0;
}
.dialog-pop-enter-active, .dialog-pop-leave-active {
  transition: transform 0.18s, opacity 0.18s;
}
.dialog-pop-enter-from, .dialog-pop-leave-to {
  opacity: 0;
  transform: scale(0.97) translateY(20px);
}
</style>
