<script setup>
import { ref, onMounted } from "vue";
import {downloadQuizImage} from "../services.js";
import LightboxImage from "../modules/lightbox/LightBoxImage.vue";

const props = defineProps({
  block: Object,
  responses: Object,
  readonly: <PERSON><PERSON><PERSON>
});
const emit = defineEmits(["input"]);

const imagePreviewUrl = ref('');
const imageLoading = ref(false);

async function loadBlockImage() {
  const url = props.block.filename;

  if (!url) {
    imagePreviewUrl.value = '';
    imageLoading.value = false;
    return;
  }
  imageLoading.value = true;
  console.log("LOADING IMAGE", url)
  try {
    const blob = await downloadQuizImage(url);
    if (!(blob instanceof Blob)) throw new Error("Response is not a Blob");
    imagePreviewUrl.value = URL.createObjectURL(blob);
  } catch (err) {
    imagePreviewUrl.value = '';
  } finally {
    imageLoading.value = false;
  }
}

function handleInput() {
  emit("input");
}

onMounted(async () => {
  await loadBlockImage();
})
</script>

<template>
  <div class="bg-white border border-gray-200 rounded-xl p-6 mb-8 shadow-sm">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-3 mb-4">
      <!-- Left: icon + title -->
      <div class="flex items-center gap-3 min-w-0">
        <span class="text-2xl">📘</span>
        <h3 class="text-lg font-bold text-gray-800 whitespace-normal break-words">
          {{ block.name || "Bloc" }}
        </h3>
      </div>

      <!-- Right: image/spinner (renders only if exists or loading) -->
      <div v-if="imageLoading || imagePreviewUrl" class="md:ml-4">
        <!-- Optional Image Loading -->
        <div v-if="imageLoading" class="flex justify-center items-center h-16 w-28">
          <span class="animate-spin w-8 h-8 border-4 border-gray-300 border-t-blue-400 rounded-full"></span>
        </div>

        <!-- Image -->
        <div v-else class="relative inline-block group">
          <LightboxImage
            :src="imagePreviewUrl"
            :alt="block?.name ? `Image de ${block.name}` : 'Block image'"
          />

          <!-- Overlay hint: magnifier icon + text -->
          <div
            class="pointer-events-none absolute bottom-2 right-2 flex items-center gap-1 rounded-full bg-black/60 text-white text-xs font-medium px-2 py-1 opacity-80
                   group-hover:opacity-100 group-focus-within:opacity-100 transition"
            aria-hidden="true"
          >
            <!-- Magnifier icon (inline SVG) -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-4 w-4" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="7"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
            <span>Agrandir</span>
          </div>
        </div>

      </div>
    </div>
    <!-- QA List -->
    <div class="text-[0.95rem]">
      <div v-for="qa in block.qa" :key="qa.id" class="bg-gray-50 rounded-lg p-1 border border-gray-100">
        <!-- Checkbox -->
        <div v-if="qa.type === 'checkbox' || qa.type === 'maybe_checkbox'" class="flex items-center gap-3">
          <input
            type="checkbox"
            :id="`checkbox-${qa.id}`"
            v-model="responses[qa.id].response_check"
            :disabled="readonly"
            class="accent-orange-500 w-5 h-5 rounded focus:ring-2 focus:ring-orange-400"
            @change="handleInput"
            true-value="1"
            false-value="0"
          />
          <label :for="`checkbox-${qa.id}`" class="text-gray-800 font-medium">{{ qa.content }}</label>
        </div>

        <!-- Free Text -->
        <div v-else-if="qa.type === 'text'">
          <label class="block font-medium mb-1 text-gray-700">{{ qa.content }}</label>
          <textarea
            v-model="responses[qa.id].response_text"
            :readonly="readonly"
            rows="2"
            placeholder="Votre réponse…"
            @input="handleInput"
            class="w-full rounded-md border-gray-300 focus:ring-orange-400 focus:border-orange-400 transition resize-y p-3 bg-white"
          />
        </div>

        <!-- Value -->
        <div v-else-if="qa.type === 'value'">
          <label class="block font-medium mb-2 text-gray-700">{{ qa.content }}</label>
          <input
            type="number"
            v-model="responses[qa.id].response_value"
            :readonly="readonly"
            placeholder="Entrez une valeur…"
            @input="handleInput"
            class="w-32 rounded-md border-gray-300 focus:ring-orange-400 focus:border-orange-400 transition p-2 bg-white"
          />
        </div>

        <!-- Fallback -->
        <div v-else>
          <p>{{ qa.content }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
