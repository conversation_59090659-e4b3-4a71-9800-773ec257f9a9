
<script setup>
import {defineProps, watch, onMounted, ref, toRefs, computed} from 'vue'
import QuizBlockGrader from "./QuizGraderBlock.vue";
import {getUserQuizForCorrection, saveUserQuizGrading} from "../services.js";
import {formatDateRange} from "../dateUtils.js";
import {toast} from "../modules/toast/toast.js";
import {calculateBlockScore, calculateQuizScore} from "./useQuizLogic.js";

const emit = defineEmits(["quizCompletionChanged", "loadingStarted", "loadingFinished"])

const props = defineProps({
  sessionSectionId: [String, Number],
  inscriptionId: [String, Number],
  manualCompletionRecord: {type: Object, default: null}
})

const { sessionSectionId, inscriptionId } = toRefs(props)

const data = ref(null)
const responsesFull = ref({})
const isLegacy = computed(() => data.value?.quiz_head?.is_legacy)
const { manualCompletionRecord } = toRefs(props)



async function loadData() {
  emit("loadingStarted")
  try {
    if (sessionSectionId.value && inscriptionId.value) {
      data.value = await getUserQuizForCorrection(inscriptionId.value, sessionSectionId.value)
      const initialResponses = data.value.answers[data.value.user.id] || {}
      const mergedResponses = {}

      function processBlock(block) {
        if (block.type !== 'content' && block.qa?.length) {
          block.qa.forEach(option => {
            const id = option.id
            const existing = initialResponses[id]
            mergedResponses[id] = existing ? {...existing} : defaultResponse(id, block.id)
          })
        }
      }

      function defaultResponse(answerId, questionId) {
        return {
          id: null,
          utilisateur_id: parseInt(data.value.user.id),
          answer_id: answerId,
          question_id: questionId,
          session_section_id: data.value.section.session_section_id,
          inscription_id: data.value.inscription.id,
          quiz_id: data.value.quiz_head.id,
          response_text: "",
          response_check: 0,
          response_value: null,
          score: null,
          date: new Date(),
          score_ai: null,
          score_author: null,
        }
      }

      data.value.blocks.forEach(processBlock)
      responsesFull.value = mergedResponses
    }
  } catch (e) {
    console.error("Failed to load quiz grading data:", e)
    toast.error("Échec du chargement. Veuillez réessayer.")
  } finally {
    emit("loadingFinished")
  }
}

async function saveData() {
  try {
    await saveUserQuizGrading(props.sessionSectionId, responsesFull.value, manualCompletionRecord.value)
    emit("quizCompletionChanged")
    toast.success("Sauvegarde réussie.")
  } catch(e) {
    console.error("Failed to save quiz grading:", e);
    // 🆕 correct toast type
    toast.error("Échec de la sauvegarde. Veuillez réessayer.")
  } finally {
  }
}


// Convenient alias
const blocks = computed(() => data.value?.blocks ?? []);

// Build responses grouped per block, as expected by calculateBlockScore/QuizScore
const responsesByBlockId = computed(() => {
  const grouped = {};
  for (const block of blocks.value) {
    if (!block?.qa?.length) continue;
    // Collect only the answers belonging to this block's QA items
    const subset = {};
    for (const qa of block.qa) {
      const r = responsesFull.value?.[qa.id];
      if (r) subset[qa.id] = r;
    }
    grouped[block.id] = subset;
  }
  return grouped;
});

// Helper to decide whether a single QA is answered
function isQaAnswered(resp) {
  if (!resp) return false;
  const hasCheck = resp.response_check === 1 || resp.response_check === true;
  const hasText  = typeof resp.response_text === 'string' && resp.response_text.trim() !== '';
  const hasValue = resp.response_value !== null && resp.response_value !== undefined && `${resp.response_value}`.trim() !== '';
  const hasScore = resp.score !== null && resp.score !== undefined && !Number.isNaN(Number(resp.score));
  return hasCheck || hasText || hasValue || hasScore;
}

// 🆕 Totals
const totalQuestions = computed(() => blocks.value.length);

const totalAnswered = computed(() => {
  let count = 0;
  for (const block of blocks.value) {
    if (!block?.qa?.length) continue;
    // A block counts as answered if at least one of its QA items is answered
    const anyAnswered = block.qa.some(qa => isQaAnswered(responsesFull.value?.[qa.id]));
    if (anyAnswered) count += 1;
  }
  return count;
});

// 🆕 Per-block scores (useful to display alongside total if you want)
const perBlockScores = computed(() => {
  return blocks.value.map(block => {
    const bResp = responsesByBlockId.value[block.id];
    const s = calculateBlockScore(block, bResp, !!isLegacy.value);
    return Number.isFinite(s) ? s : 0;
  });
});

// 🆕 Total score (raw sum or normalised to maxScore)
const totalScore = computed(() => {
  return calculateQuizScore(
    blocks.value,
    responsesByBlockId.value,
    props.maxScore ?? 0
  );
});

// Optional: show the raw sum too, even when normalising
const totalScoreRaw = computed(() => perBlockScores.value.reduce((a, b) => a + b, 0));

onMounted(() => loadData())
watch([sessionSectionId, inscriptionId], () => loadData())

const stats = computed(() => ({
  totalQuestions: totalQuestions.value,
  totalAnswered: totalAnswered.value,
  totalScore: totalScore.value,
  totalScoreRaw: totalScoreRaw.value,
}));

defineExpose({
  saveData,
  // expose individual refs if you prefer:
  totalQuestions,
  totalAnswered,
  totalScore,
  totalScoreRaw,
  // or a bundled computed:
  stats,
});

</script>


<template>
  <div >


    <!-- Content -->
    <div v-if="data" class="busy-content">
      <div v-if="isLegacy" class="legacy-warning">
        ⚠️ Ce formulaire a été migré à partir de l'ancien format de données.
        Les questions à choix multiples doivent être corrigées manuellement par option,
        et le score le plus élevé parmi toutes les options sera pris en compte comme score final de la question.
      </div>

      <div class="quiz-container">
        <div v-for="block in data.blocks" :key="block.id" class="quiz-block">
          <QuizBlockGrader
            :block="block"
            :sessionSectionId="sessionSectionId"
            :responses="responsesFull"
            :includeScoring="data.quiz_head.requires_scoring"
            :isLegacy="data.quiz_head.is_legacy"
          />
        </div>
      </div>
    </div>
  </div>
</template>


<style scoped>

/* Fade for overlay */
.fade-enter-active, .fade-leave-active { transition: opacity 0.15s; }
.fade-enter-from, .fade-leave-to { opacity: 0; }

/* (Your existing styles remain as-is below) */
.grading-header { background: #f9fafb; border: 1px solid #e0e6ed; border-radius: 12px; padding: 24px; margin-bottom: 24px; font-size: 1.1em; position: sticky; top: 0; z-index: 10; backdrop-filter: blur(8px); }
.header-top { display: flex; justify-content: space-between; align-items: center; gap: 1.5rem; margin-bottom: 1rem; }
.header-top-left { display: flex; flex-direction: column; }
.header-top-right { font-size: 1.4em; font-weight: 700; color: #2c3e50; }
.header-mid { display: flex; justify-content: space-between; flex-wrap: wrap; margin-bottom: 1rem; gap: 2rem; }
.header-block { display: flex; flex-direction: row; align-items: center; flex: 1 1 60%; min-width: 200px; gap: 0.5rem; }
.header-block.fixed-width { flex: 0 0 auto; }
.header-block .label { font-size: 0.9em; font-weight: 600; color: #7f8c8d; white-space: nowrap; }
.header-block .value { font-size: 1.1em; font-weight: 700; color: #2c3e50; }
.header-block .sub-value { font-size: 0.85em; color: #95a5a6; margin-left: 0.5rem; }
.header-user { display: flex; justify-content: center; align-items: center; margin-bottom: 1rem; }
.header-user .value { font-size: 1.2em; font-weight: 700; color: #34495e; }
.header-stats { display: flex; flex-wrap: wrap; gap: 1.5rem; margin-top: 1rem; border-top: 1px solid #e0e6ed; padding-top: 1rem; align-items: center; }
.stat-item { display: flex; align-items: center; gap: 0.5rem; font-size: 0.95em; color: #34495e; }
.stat-label { font-weight: 600; margin-left: 4px; }
.stat-value { font-weight: 700; font-size: 1.1em; }
.save-btn { background-color: #4caf50; color: white; font-weight: 600; padding: 10px 20px; border-radius: 8px; transition: all 0.3s ease; border: none; cursor: pointer; }
.save-btn:hover { background-color: #45a049; }
.legacy-warning { background-color: #fff9db; border: 1px solid #f0c36d; color: #8a6d3b; padding: 16px; border-radius: 8px; margin: 20px 0; font-size: 0.95em; line-height: 1.5; }
.manual-completion-panel { margin-top: 1.5em; padding: 1em 0 0 0; border-top: 1px solid #e4e4e4; display: flex; flex-direction: column; gap: 0.6em; background: #fafbfc; border-radius: 8px; }
.manual-label { font-weight: 600; font-size: 0.97em; color: #41506b; margin-bottom: 0.2em; display: block; }
.manual-obs-textarea { width: 100%; min-height: 40px; border-radius: 4px; border: 1px solid #e5e7eb; padding: 8px; font-size: 1em; color: #374151; resize: vertical; background: #fff; }
.manual-passed-group { display: flex; align-items: center; gap: 1.3em; margin-top: 0.3em; }
.manual-passed-options label { font-weight: 500; color: #41506b; margin-right: 1em; display: inline-flex; align-items: center; gap: 0.25em; cursor: pointer; }
.manual-passed-options input[type="radio"] { accent-color: #2563eb; margin-right: 3px; }
.badge-eval { padding: 2px 12px; border-radius: 8px; font-weight: 600; font-size: 0.95em; margin-left: 6px; margin-right: 8px; background: #f3f4f6; color: #64748b; border: 1px solid #e5e7eb; display: inline-block; }
.badge-eval-ok { background: #e7fbe8; color: #227a3b; border-color: #b8e7be; }
.badge-eval-ko { background: #faeaea; color: #b42020; border-color: #fecaca; }
.manual-toggle-btn { background: none; border: none; color: #2563eb; font-weight: 600; cursor: pointer; font-size: 1em; text-decoration: underline dotted; padding: 0; margin: 0; outline: none; }
</style>
