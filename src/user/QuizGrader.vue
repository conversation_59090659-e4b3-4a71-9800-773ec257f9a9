<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { toast } from "../modules/toast/toast.js";
import { getParticipantsForQuizCorrection } from "../services.js";
import QuizGraderOne from "./QuizGraderOne.vue";

// ──────────────────────────────────────────────────────────────────────────────
// Types
// ──────────────────────────────────────────────────────────────────────────────
// Added: total_score (raw points obtained across all questions)
type ParticipantRaw = {
  nom: string | null;
  prenom: string | null;
  total_questions: number | string | null;
  total_answered: string | number | null;
  total_scored: string | number | null; // how many answers have been graded/corrected
  total_score?: string | number | null; // NEW: total points earned (for /10 chip)
  inscription_id?: number | null;
  session_section_id?: number | null;
};

const props = defineProps<{
  course?: Record<string, unknown>;
  section?: { session_section_id?: number } | null;
}>();

const participants = ref<ParticipantRaw[]>([]);
const selectedIdx = ref<number | null>(null);
const quizGrader = ref<InstanceType<typeof QuizGraderOne> | null>(null);


// 🆕 busy state
const isLoading = ref(false)
const isSaving  = ref(false)
const isBusy    = computed(() => isLoading.value || isSaving.value)


function safeNum(v: string | number | null | undefined): number {
  if (v === null || v === undefined || v === "") return 0;
  const n = Number(v);
  return Number.isFinite(n) ? n : 0;
}

function formatOneDecimal(n: number): string {
  // Avoid trailing ".0" if integer
  return Math.abs(n - Math.round(n)) < 1e-9 ? String(Math.round(n)) : n.toFixed(1);
}

const normalized = computed(() =>
  participants.value.map((p, idx) => {
    const answered = safeNum(p.total_answered);
    const scored = safeNum(p.total_scored);
    const total = safeNum(p.total_questions);
    const totalScore = safeNum(p.total_score);

    const fullName = [p.prenom, p.nom].filter(Boolean).join(" ");

    const score = total > 0 ? (totalScore / total) : 0;

    return {
      index: idx,
      fullName,
      answered,
      scored, // how many answers graded
      total,   // how many questions exist
      totalScore, // raw points earned
      score,
      scoreTooltip: `calculated from ${totalScore} / ${total}`,
      needsCorrection: scored !== answered,
      notComplete: answered !== total,
      raw: p,
    };
  })
);

async function _fetch(idx: number | null = null) {
  try {
    if (props.section?.session_section_id) {
      participants.value = await getParticipantsForQuizCorrection(
        props.section.session_section_id
      );
      const len = participants.value.length;
      if (len) {
        const next = (typeof idx === 'number' && idx >= 0 && idx < len) ? idx : 0;
        selectedIdx.value = next;
      } else {
        selectedIdx.value = null;
      }
    }
  } catch (e) {
    console.error("Failed to load quiz:", e);
    toast.error("Impossible de charger le quiz pour le moment.");
  } finally {
  }
}

onMounted(_fetch);

function handleParticipantSelection(itemIndex: number) {
  selectedIdx.value = itemIndex;
}

const selectedItem = computed(() =>
  selectedIdx.value != null ? normalized.value[selectedIdx.value] : null
);

async function handleSave() {
  if (!selectedItem.value) return;
  isSaving.value = true;
  try{
    await quizGrader.value?.saveData();
  } catch (e) {
    console.error("Failed to save quiz:", e);
  } finally {
    isSaving.value = false;
  }

}

function setLoading(_isLoading: boolean) {
  isLoading.value = _isLoading;
}

async function handleQuizCompletionChanged() {
  await _fetch(selectedIdx.value);
}
</script>

<template>
  <!-- Fill viewport; prevent page-level scroll -->
  <div class="flex flex-col h-full busy-wrapper" :class="{ 'is-busy': isBusy }" :aria-busy="isBusy">
    <!-- Overlay spinner -->
    <transition v-if="isBusy" name="fade">
      <div v-if="isBusy" class="busy-overlay" role="status" aria-live="polite">
        <div class="spinner" aria-hidden="true"></div>
        <div class="busy-text">
          {{ isLoading ? 'Chargement…' : 'Sauvegarde…' }}
        </div>
      </div>
    </transition>

    <div class="busy-content">

      <!-- Sticky global header with compact height when nothing selected -->
      <header class="sticky top-0 z-30 bg-white/95 backdrop-blur border-b shadow-sm">
        <div
          class="max-w-full mx-auto px-3 sm:px-4 flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between"
          :class="selectedItem ? 'py-2.5' : 'py-1.5'"
        >
          <div class="min-w-0">
            <h2 class="text-base sm:text-lg font-semibold text-gray-900 truncate">
              {{ selectedItem?.fullName || "Sélectionnez un participant" }}
            </h2>
            <p v-if="selectedItem" class="text-xs sm:text-sm text-gray-600 flex flex-wrap items-center gap-2">
              <!-- Cor/Rep: graded vs answered -->
              <span
                class="px-2 py-0.5 rounded-full border"
                :class="selectedItem.needsCorrection
                  ? 'bg-amber-50 border-amber-300 text-amber-700'
                  : 'bg-teal-50 border-teal-300 text-teal-700'"
                title="Corrections effectuées / Réponses données"
                aria-label="Corrections effectuées sur réponses données"
              >
                <strong>Cor/Rep:</strong>
                {{ selectedItem.scored }} / {{ selectedItem.answered }}
              </span>

              <!-- Rep/Tot: answered vs total -->
              <span
                class="px-2 py-0.5 rounded-full border"
                :class="selectedItem.notComplete
                  ? 'bg-amber-50 border-amber-300 text-amber-700'
                  : 'bg-teal-50 border-teal-300 text-teal-700'"
                title="Réponses données / Questions totales"
                aria-label="Réponses données sur questions totales"
              >
                <strong>Rep/Tot:</strong>
                {{ selectedItem.answered }} / {{ selectedItem.total }}
              </span>

              <!-- Score (/10) -->
              <span
                class="px-2 py-0.5 rounded-full border bg-indigo-50 border-indigo-300 text-indigo-700"
                :title="selectedItem.scoreTooltip"
                aria-label="Score total normalisé sur 10"
              >
                <strong>Score:</strong>
                {{ (selectedItem.score * 20).toFixed(1) }} / 20
              </span>
            </p>
          </div>
          <div class="flex items-center gap-2">
            <button
              type="button"
              class="inline-flex items-center justify-center rounded-md border border-teal-600 bg-teal-600 text-white px-3 py-1.5 text-sm font-medium shadow-sm hover:bg-teal-700 hover:border-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="!selectedItem"
              @click="handleSave"
              aria-label="Enregistrer"
              title="Enregistrer"
            >
              <svg class="h-4 w-4 mr-1.5" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                <path d="M17 3H5a2 2 0 0 0-2 2v14l4-4h10a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2zM7 7h8v2H7V7z"/>
              </svg>
              Enregistrer
            </button>
          </div>
        </div>
      </header>

      <!-- Content area takes remaining height; children handle their own scroll -->
      <div class="flex-1 min-h-0 overflow-hidden">
        <div class="h-full min-h-0 grid grid-cols-1 md:grid-cols-[280px_1fr]">
          <!-- LEFT column: independent scroll -->
          <aside class="h-full min-h-0 overflow-y-auto px-3 py-2">
            <div class="flex items-center justify-between mb-2">
              <span class="font-semibold text-gray-800">Participants</span>
              <span class="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full" title="Nombre total de participants listés">
                {{ normalized.length }}
              </span>
            </div>

            <div v-if="!normalized.length" class="text-sm text-gray-500 p-2">
              Aucun participant.
            </div>

            <ul v-else class="space-y-2" role="listbox">
              <li
                v-for="p in normalized"
                :key="p.index"
                @click="handleParticipantSelection(p.index)"
                class="cursor-pointer px-3 py-2 flex flex-col hover:bg-gray-50 transition rounded-md"
                :class="selectedIdx === p.index ? 'bg-teal-50 ring-1 ring-teal-400' : ''"
                role="option"
                :aria-selected="selectedIdx === p.index"
                tabindex="0"
                @keydown.enter.prevent="handleParticipantSelection(p.index)"
                @keydown.space.prevent="handleParticipantSelection(p.index)"
              >
                <div class="font-medium text-sm truncate" :title="p.fullName || '—'">{{ p.fullName || "—" }}</div>
                <div class="flex flex-wrap gap-2 mt-1 text-xs">
                  <!-- Cor/Rep chip -->
                  <span
                    class="px-2 py-0.5 rounded-full border"
                    :class="p.needsCorrection
                      ? 'bg-amber-50 border-amber-300 text-amber-700'
                      : 'bg-teal-50 border-teal-300 text-teal-700'"
                    title="Corrections effectuées / Réponses données"
                  >
                    <strong>Cor/Rep:</strong> {{ Math.min(p.scored, p.answered) }} / {{ p.answered }}
                  </span>
                  <!-- Rep/Tot chip -->
                  <span
                    class="px-2 py-0.5 rounded-full border"
                    :class="p.notComplete
                      ? 'bg-amber-50 border-amber-300 text-amber-700'
                      : 'bg-teal-50 border-teal-300 text-teal-700'"
                    title="Réponses données / Questions totales"
                  >
                    <strong>Rep/Tot:</strong> {{ p.answered }} / {{ p.total }}
                  </span>
                  <!-- Score (/20) chip -->
                  <span
                    class="px-2 py-0.5 rounded-full border bg-indigo-50 border-indigo-300 text-indigo-700"
                    :title="p.scoreTooltip"
                  >
                    <strong>Score:</strong> {{ (p.score * 20).toFixed(1) }} / 20
                  </span>
                </div>
              </li>
            </ul>
          </aside>

          <!-- RIGHT column: independent scroll + subtle separator on md+ -->
          <main class="h-full min-h-0 overflow-y-auto px-3 py-2 md:border-l md:border-gray-200">
            <div v-if="selectedItem" class="space-y-2">
              <QuizGraderOne
                ref="quizGrader"
                :inscriptionId="selectedItem.raw.inscription_id"
                :sessionSectionId="selectedItem.raw.session_section_id"
                @quizCompletionChanged="handleQuizCompletionChanged"
                @loadingStarted="setLoading(true)"
                @loadingFinished="setLoading(false)"
              />
            </div>
            <div v-else class="text-sm text-gray-500">
              Sélectionnez un participant dans la liste.
            </div>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Container that we dim/lock when busy */
.busy-wrapper {
  position: relative;
}

/* Prevent interactions when busy */
.is-busy .busy-content {
  pointer-events: none;
  user-select: none;
  filter: blur(1px);
  opacity: 0.6;
}

/* Full-cover overlay */
.busy-overlay {
  position: fixed;        /* 🆕 full viewport */
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* 🆕 centers vertically & horizontally */
  backdrop-filter: blur(2px);
  background: rgba(255, 255, 255, 0.7);
  z-index: 9999;          /* ensure above everything */
  text-align: center;
  gap: 12px;
}
.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e5e7eb;
  border-top-color: #2563eb;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}
@keyframes spin { to { transform: rotate(360deg); } }
.busy-text {
  font-weight: 600;
  color: #374151;
}

.busy-text {
  font-weight: 600;
  color: #374151;
}
</style>
