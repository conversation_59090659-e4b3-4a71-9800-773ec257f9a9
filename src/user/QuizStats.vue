<template>
  <div v-if="data" class="text-slate-900 bg-white">
    <!-- Wrap HEADER + MAIN so we can clone exactly this for printing -->
    <div ref="printRoot" class="report-root">
      <!-- Header (no print:hidden; we print it) -->
      <header class="border-b border-slate-200 bg-white px-4 py-3">
        <h1 class="text-2xl font-bold text-[var(--color-amf-primary)]">{{ title }}</h1>
        <p class="text-xs text-slate-500 mt-0.5">
          <span v-if="data.session?.numsession">{{ data.session.numsession }}</span>
          <span v-if="data.session?.start_time"> · {{ data.session.start_time }}</span>
        </p>
        <div class="mt-2 flex gap-2 print-actions">
          <button
            class="rounded-full border border-slate-200 bg-slate-50 px-3 py-1.5 text-sm hover:brightness-95"
            @click="openStandalone"
          >
            Imprimer
          </button>
          <button
            class="rounded-full border border-slate-200 bg-slate-50 px-3 py-1.5 text-sm hover:brightness-95"
            @click="appendixOpen = !appendixOpen"
          >
            {{ appendixOpen ? 'Masquer' : 'Afficher' }} l'annexe
          </button>
        </div>
      </header>

      <main class="mx-auto max-w-[1100px] p-4 print:p-[10mm]">
        <!-- Overview -->
        <section>
          <h2 class="mt-5 mb-2 text-xl font-semibold text-[var(--color-amf-primary)]">Vue d'ensemble</h2>

          <!-- Stats grid -->
          <div class="grid gap-3 md:grid-cols-4 print-stats-grid">
            <div class="rounded-2xl border border-slate-200 p-4">
              <div class="text-2xl font-bold">{{ participants.length }}</div>
              <div class="text-xs text-slate-500">Participants</div>
            </div>
            <div class="rounded-2xl border border-slate-200 p-4">
              <div class="text-2xl font-bold">{{ blocks.length }}</div>
              <div class="text-xs text-slate-500">Questions</div>
            </div>
            <div class="rounded-2xl border border-slate-200 p-4">
              <div class="text-2xl font-bold">{{ avgScore.toFixed(2) }} / {{ 20 }}</div>
              <div class="text-xs text-slate-500">Score moyen</div>
            </div>
            <div class="rounded-2xl border border-slate-200 p-4">
              <div class="text-2xl font-bold">{{ medianScore.toFixed(2) }} / {{ 20 }}</div>
              <div class="text-xs text-slate-500">Médiane</div>
            </div>
          </div>

          <!-- Two-up cards -->
          <div class="mt-3 grid gap-3 md:grid-cols-2 print-two-up">
            <div class="rounded-2xl border border-slate-200 p-4">
              <h3 class="mb-2 font-medium">Distribution des scores</h3>
              <div>
                <div v-for="(count, i) in histogram" :key="i" class="mb-1">
                  <div
                    class="h-5 w-full overflow-hidden rounded-full border border-slate-200 bg-[repeating-linear-gradient(90deg,#f8fafc,#f8fafc_10px,#eef2f7_10px,#eef2f7_20px)]"
                  >
                    <i
                      class="block h-full bg-[linear-gradient(90deg,rgba(0,0,0,0.14),rgba(0,0,0,0.06))]"
                      :style="{ width: (scoreRows.length ? (count/scoreRows.length)*100 : 0) + '%' }"
                    ></i>
                  </div>
                  <div class="mt-0.5 flex justify-between text-xs text-slate-500">
                    <span>{{ i*10 }}–{{ (i+1)*10 }}%</span><span>{{ count }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="rounded-2xl border border-slate-200 p-4">
              <h3 class="mb-2 font-medium">Taux de réussite</h3>
              <div
                class="h-5 w-full overflow-hidden rounded-full border border-slate-200 bg-[repeating-linear-gradient(90deg,#f8fafc,#f8fafc_10px,#eef2f7_10px,#eef2f7_20px)]"
              >
                <i
                  class="block h-full bg-[linear-gradient(90deg,rgba(0,0,0,0.14),rgba(0,0,0,0.06))]"
                  :style="{ width: (passRate*100)+'%' }"
                ></i>
              </div>
              <div class="mt-0.5 flex justify-between text-xs text-slate-500">
                <span>Pass rate</span><span>{{ Math.round(passRate*100) }}%</span>
              </div>
              <div class="text-xs text-slate-500">
                {{ passes }} sur {{ scoreRows.length }} à ≥ {{ Math.round(passMark*100) }}%
              </div>
            </div>
          </div>
        </section>

        <!-- Questions -->
        <section class="print:break-before-page">
          <h2 class="mt-6 mb-2 text-xl font-semibold text-[var(--color-amf-primary)]">Questions (les moins réussies d'abord)</h2>
          <div class="grid gap-2">
            <article v-for="it in sortedBlocks" :key="it.b.id" class="rounded-2xl border border-slate-200 p-3">
              <div class="flex flex-wrap items-baseline justify-between gap-3">
                <div class="font-semibold">{{ qNum(it.b) }}. {{ it.b.name }}</div>
                <div class="flex flex-wrap items-center gap-2">
                  <span class="rounded-full border border-slate-200 px-2 py-0.5 text-xs text-slate-700">
                    {{ it.b.type === 'free_text' ? 'Texte' : 'Choix' }}
                  </span>
                  <span :class="['font-semibold', pctClass(it.passOrMean)]">
                    {{ fmtPct(it.passOrMean) }} correct
                  </span>
                  <span class="rounded-full border border-slate-200 px-2 py-0.5 text-xs text-slate-700">
                    {{ it.meta?.count ?? it.answeredCount ?? 0 }} réponses
                  </span>
                  <span v-if="it.meta?.std != null" class="rounded-full border border-slate-200 px-2 py-0.5 text-xs text-slate-700">
                    σ {{ Number(it.meta.std).toFixed(2) }}
                  </span>
                </div>
              </div>

              <div v-if="it.b.type === 'free_text'" class="mt-2">
                <div class="grid gap-2 md:grid-cols-4">
                  <div><div class="text-xs text-slate-500">Répondu</div><div class="text-2xl font-bold">{{ it.freeStats.answered }}</div></div>
                  <div><div class="text-xs text-slate-500">Correct</div><div class="text-2xl font-bold">{{ it.freeStats.correct }}</div></div>
                  <div><div class="text-xs text-slate-500">Partiel</div><div class="text-2xl font-bold">{{ it.freeStats.partial }}</div></div>
                  <div><div class="text-xs text-slate-500">Incorrect</div><div class="text-2xl font-bold">{{ it.freeStats.incorrect }}</div></div>
                </div>
                <details class="mt-2">
                  <summary class="cursor-pointer text-sm">Exemples ({{ it.samples.length }})</summary>
                  <div
                    v-for="(s, i) in it.samples.slice(0,3)"
                    :key="i"
                    class="mt-1 border-l-4 border-slate-200 bg-slate-50 p-2 text-sm"
                  >
                    {{ s }}
                  </div>
                </details>
              </div>

              <div v-else class="mt-2 space-y-2">
                <div v-for="o in it.options" :key="o.id" class="text-sm">
                  <div
                    class="h-5 w-full overflow-hidden rounded-full border border-slate-200 bg-[repeating-linear-gradient(90deg,#f8fafc,#f8fafc_10px,#eef2f7_10px,#eef2f7_20px)]"
                  >
                    <i
                      class="block h-full bg-[linear-gradient(90deg,rgba(0,0,0,0.14),rgba(0,0,0,0.06))]"
                      :style="{ width: ((o.count/participants.length)*100) + '%' }"
                    ></i>
                  </div>
                  <div class="mt-0.5 flex justify-between text-xs text-slate-500">
                    <span>
                      <span class="font-medium text-slate-700">{{ o.content }}</span>
                      <span v-if="o.correct" class="text-emerald-600">(correct)</span>
                    </span>
                    <span>{{ o.count }} ({{ Math.round((o.count/participants.length)*100) }}%)</span>
                  </div>
                </div>
              </div>
            </article>
          </div>
        </section>

        <!-- Matrix -->
        <section class="print:break-before-page">
          <h2 class="mt-6 mb-2 text-xl font-semibold text-[var(--color-amf-primary)]">Matrice des réponses</h2>
          <div class="mb-2 flex gap-4 text-xs text-slate-500">
            <span><span class="font-bold text-emerald-600">✔</span> correct</span>
            <span><span class="font-bold text-amber-500">◐</span> partiel</span>
            <span><span class="font-bold text-rose-600">✘</span> incorrect</span>
            <span><span class="text-slate-500">·</span> non répondu</span>
          </div>

          <div
            v-for="(chunk, sIdx) in tableSlices"
            :key="sIdx"
            class="overflow-x-auto mt-3"
          >
            <div class="mb-1 text-xs text-slate-500">
              Questions <span class="font-medium">Q{{ chunk.from }}–Q{{ chunk.to }}</span>
            </div>

            <table class="w-full border-collapse text-sm">
              <thead>
                <tr>
                  <th class="w-24 sticky left-0 z-[1] bg-white px-2 py-1 text-left font-medium border border-slate-200">
                    Participant
                  </th>
                  <th
                    v-for="b in chunk.blocks"
                    :key="b.id"
                    class="border border-slate-200 px-1 py-1 align-bottom"
                    :title="b.name"
                  >
                    <div class="vertical-q rotate-180 [writing-mode:vertical-rl]">
                      Q{{ qNum(b) }}
                    </div>
                  </th>
                  <th class="border border-slate-200 px-2 py-1 font-medium">Total</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="p in participants" :key="p.id">
                  <td class="sticky left-0 z-[1] bg-white px-2 py-1 text-left border border-slate-200">
                    {{ p.label }}
                  </td>
                  <td
                    v-for="b in chunk.blocks"
                    :key="b.id"
                    class="border border-slate-200 px-1 py-1 text-center font-mono"
                    :title="tooltipFor(p,b)"
                  >
                    <span v-html="markFor(p,b)"></span>
                  </td>
                  <td class="border border-slate-200 px-1 py-1 text-center">
                    {{ totalFor(p, chunk.blocks).toFixed(2) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        <!-- Appendix -->
        <section v-show="appendixOpen" class="print:break-before-page">
          <h2 class="mt-6 mb-2 text-xl font-semibold text-[var(--color-amf-primary)]">Annexe — Réponses texte</h2>
          <div v-if="textBlocks.length === 0" class="text-sm text-slate-500">Aucune réponse texte.</div>
          <div v-else class="space-y-3">
            <div v-for="b in textBlocks" :key="b.id" class="rounded-2xl border border-slate-200 p-3">
              <h3 class="font-medium">Q{{ b.display_order }}. {{ b.name }}</h3>
              <div v-for="p in participants" :key="p.id" class="mt-2 border-l-4 border-slate-200 bg-slate-50 p-2 text-sm">
                <span :class="gradeClass(gradeFor(p,b))" class="mr-1 font-bold">{{ gradeMark(gradeFor(p,b)) }}</span>
                <span class="font-medium">{{ p.label }}:</span>
                <span class="ml-1">{{ freeTextFor(p,b) }}</span>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import type { Course, Section } from "./courses/useCoursesLogic";
import { getQuizStats } from "../services";

// Props: pass the exact object you shared
const props = defineProps<{ course: Course; section: Section; passMark?: number; matrixColChunk?: number }>()
const data = ref<any>(null)
const passMark = computed(() => props.passMark ?? 0.6)
const matrixColChunk = computed(() => props.matrixColChunk ?? 20)

const appendixOpen = ref(true)

/* ---------- Indexing & numbering ---------- */
const blocks = computed(() => {
  const raw = (data.value?.blocks || []).slice()
  raw.sort((a: any, b: any) => {
    const ao = a.display_order ?? 0
    const bo = b.display_order ?? 0
    if (ao !== bo) return ao - bo
    return (a.id ?? 0) - (b.id ?? 0) // tie-break by id
  })
  return raw.map((b: any, i: number) => ({ ...b, _seq: i + 1 })) // assign sequential numbers
})

const seqById = computed<Record<string, number>>(() => {
  const m: Record<string, number> = {}
  for (const b of blocks.value) m[String(b.id)] = b._seq
  return m
})

function qNum(b: any) {
  return b?._seq ?? seqById.value[String(b?.id)] ?? ''
}

const textBlocks = computed(() => blocks.value.filter((b: any) => b.type === 'free_text'))

const blocksById = computed<Record<string, any>>(() => {
  const src = data.value?.blocks_by_id
  if (src && Object.keys(src).length) return src
  const map: Record<string, any> = {}
  for (const b of blocks.value) map[String(b.id)] = b
  return map
})

const qaIndex = computed<Record<string, { blockId: string; content: string; correct: boolean }>>(() => {
  const idx: Record<string, any> = {}
  Object.values(blocksById.value).forEach((b: any) => {
    ;(b.qa || []).forEach((q: any) => {
      idx[String(q.id)] = { blockId: String(b.id), content: q.content, correct: !!q.option_is_correct }
    })
  })
  return idx
})

const participants = computed(() => {
  const obj = data.value?.inscriptions || {}
  return Object.values(obj).map((insc: any) => ({
    id: insc.id,
    utilisateur_id: insc.utilisateur_id,
    label: (insc.nom?.trim().slice(0, 4) || '') + ' ' + (insc.prenom?.trim().charAt(0) || ''),
    answers: insc.answers || {},
    blockEval: insc.block_evaluations || {},
  }))
})

/* ---------- Scores overview ---------- */
const theoreticalMax = computed(() => blocks.value.length)

const scoreRows = computed(() => {
  return participants.value.map((p) => {
    let total = 0
    let answered = 0
    for (const b of blocks.value) {
      const be = p.blockEval[String(b.id)]
      const sc = be && typeof be.score === 'number' ? be.score : 0
      total += sc
      if (be && Number(be.answered) === 1) answered++
    }
    return { id: p.id, label: p.label, total, pct: theoreticalMax.value ? total / theoreticalMax.value : 0, answered }
  })
})

const scores = computed(() => scoreRows.value.map((r) => 20 * r.total / theoreticalMax.value))
const avgScore = computed(() => (scores.value.length ? scores.value.reduce((a, b) => a + b, 0) / scores.value.length : 0))
const medianScore = computed(() => {
  const s = scores.value.slice().sort((a, b) => a - b)
  const m = Math.floor(s.length / 2)
  return s.length % 2 ? s[m] : (s[m - 1] + s[m]) / 2
})

const passes = computed(() => scoreRows.value.filter((r) => r.pct >= passMark.value).length)
const passRate = computed(() => (scoreRows.value.length ? passes.value / scoreRows.value.length : 0))

const histogram = computed(() => {
  const bins = 10
  const hist = Array.from({ length: bins }, () => 0)
  for (const r of scoreRows.value) {
    const pct = Math.max(0, Math.min(1, r.pct))
    let idx = Math.floor(pct * bins)
    if (idx === bins) idx = bins - 1
    hist[idx]++
  }
  return hist
})

/* ---------- Per-block stats ---------- */
const evalByBlock = computed<Record<string, any>>(() => data.value?.evaluation_by_block || {})

async function loadData() {
  try {
    data.value = await getQuizStats(props.section.session_section_id)
  } catch (e) {
    console.error("Failed to load quiz stats:", e)
  }
}

function choiceDistributionForBlock(blockId: string) {
  const b = blocksById.value[blockId]
  const options: any[] = (b?.qa || [])
  const counts: Record<string, number> = {}
  for (const o of options) counts[o.content] = 0
  for (const p of participants.value) {
    for (const o of options) {
      const rec = p.answers[String(o.id)]
      if (rec && Number(rec.response_check) === 1) {
        counts[o.content] = (counts[o.content] || 0) + 1
      }
    }
  }
  return { options, counts }
}

const blockItems = computed(() => {
  return blocks.value.map((b: any) => {
    const bid = String(b.id)
    const ev = evalByBlock.value[bid] || null
    const passOrMean = ev ? ev.pass_rate ?? ev.difficulty_index ?? ev.mean ?? null : null

    if (b.type === 'free_text') {
      const freeStats = { answered: 0, correct: 0, partial: 0, incorrect: 0 }
      const samples: string[] = []
      const qa = (blocksById.value[bid]?.qa || [])[0]
      for (const p of participants.value) {
        const be = p.blockEval[bid]
        if (be && Number(be.answered) === 1) freeStats.answered++
        const sc = be && typeof be.score === 'number' ? be.score : null
        if (sc != null) {
          if (sc >= 0.999) freeStats.correct++
          else if (sc <= 0.001) freeStats.incorrect++
          else freeStats.partial++
        }
        if (qa) {
          const a = p.answers[String(qa.id)]
          if (a && a.response_text) {
            const g = sc == null ? 'na' : sc >= 0.999 ? 'correct' : sc <= 0.001 ? 'incorrect' : 'partial'
            const mark = g === 'correct' ? '✔' : g === 'partial' ? '◐' : g === 'incorrect' ? '✘' : '·'
            samples.push(`${mark} ${p.label}: ${a.response_text}`)
          }
        }
      }
      return { b, meta: ev, passOrMean, answeredCount: freeStats.answered, options: [], freeStats, samples }
    }

    const { options, counts } = choiceDistributionForBlock(bid)
    const optionsWithCounts = options.map((o) => ({ ...o, count: counts[o.content] || 0 }))

    return { b, meta: ev, passOrMean, answeredCount: null, options: optionsWithCounts, freeStats: null, samples: [] }
  })
})

const sortedBlocks = computed(() => {
  const items = blockItems.value.slice()
  items.sort((a: any, b: any) => {
    const ak = a.passOrMean == null ? -1 : a.passOrMean
    const bk = b.passOrMean == null ? -1 : b.passOrMean
    if (ak === -1 && bk === -1) return 0
    if (ak === -1) return -1
    if (bk === -1) return 1
    return ak - bk
  })
  return items
})

/* ---------- Matrix helpers ---------- */
const tableSlices = computed(() => {
  const size = matrixColChunk.value
  const slices: { blocks: any[]; from: number; to: number }[] = []
  for (let i = 0; i < blocks.value.length; i += size) {
    const slice = blocks.value.slice(i, i + size)
    const from = slice[0]?._seq ?? i + 1
    const to   = slice[slice.length - 1]?._seq ?? i + slice.length
    slices.push({ blocks: slice, from, to })
  }
  return slices
})

function selectionsFor(p: any, b: any) {
  const bid = String(b.id)
  const options: any[] = (blocksById.value[bid]?.qa || [])
  const labels: string[] = []
  for (const o of options) {
    const rec = p.answers[String(o.id)]
    if (rec && Number(rec.response_check) === 1) labels.push(o.content)
  }
  return labels
}

function tooltipFor(p: any, b: any) {
  if (b.type === 'free_text') {
    const qa = (blocksById.value[String(b.id)]?.qa || [])[0]
    const ans = qa ? p.answers[String(qa.id)] : null
    return ans?.response_text || selectionsFor(p, b).join(', ')
  }
  return selectionsFor(p, b).join(', ')
}

function markFor(p: any, b: any) {
  const be = p.blockEval[String(b.id)]
  const sc = be && typeof be.score === 'number' ? be.score : null
  if (sc == null) return '<span class="text-slate-500">·</span>'
  if (sc >= 0.999) return '<span class="font-bold text-emerald-600">✔</span>'
  if (sc <= 0.001) return '<span class="font-bold text-rose-600">✘</span>'
  return '<span class="font-bold text-amber-500">◐</span>'
}

function totalFor(p: any, slice: any[]) {
  let t = 0
  for (const b of slice) {
    const be = p.blockEval[String(b.id)]
    t += be && typeof be.score === 'number' ? be.score : 0
  }
  return t
}

function freeTextFor(p: any, b: any) {
  const qa = (blocksById.value[String(b.id)]?.qa || [])[0]
  const ans = qa ? p.answers[String(qa.id)] : null
  return ans?.response_text || ''
}

function gradeFor(p: any, b: any) {
  const be = p.blockEval[String(b.id)]
  const sc = be && typeof be.score === 'number' ? be.score : null
  if (sc == null) return 'na'
  if (sc >= 0.999) return 'correct'
  if (sc <= 0.001) return 'incorrect'
  return 'partial'
}

function gradeMark(g: string) {
  return g === 'correct' ? '✔' : g === 'partial' ? '◐' : g === 'incorrect' ? '✘' : '·'
}
function gradeClass(g: string) {
  return g === 'correct' ? 'text-emerald-600' : g === 'partial' ? 'text-amber-500' : g === 'incorrect' ? 'text-rose-600' : 'text-slate-500'
}

/* ---------- UI helpers ---------- */
const title = computed(() => {
  const a = data.value?.session?.libellestage || data.value?.quiz_head?.name || 'Quiz'
  const b = data.value?.section?.name
  return b ? `${a} — ${b}` : a
})
function fmtPct(p: number | null) {
  if (p == null || Number.isNaN(p)) return '—%'
  return `${Math.round(p * 100)}%`
}
function pctClass(p: number | null) {
  if (p == null) return 'text-slate-500'
  if (p >= 0.75) return 'text-emerald-600'
  if (p >= 0.4) return 'text-amber-500'
  return 'text-rose-600'
}

/* ---------- Print in new window ---------- */
const printRoot = ref<HTMLElement | null>(null)

function openStandalone() {
  const root = printRoot.value
  if (!root) return

  // Capture styles from the current page (Tailwind + scoped)
  const styleTags = Array.from(document.querySelectorAll('style'))
    .map(s => s.outerHTML)
    .join('\n')

  const linkTags = Array.from(document.querySelectorAll<HTMLLinkElement>('link[rel="stylesheet"]'))
    .map(l => {
      const a = document.createElement('a'); a.href = l.href
      return `<link rel="stylesheet" href="${a.href}">`
    })
    .join('\n')

  // Print helper CSS
  const helperCSS = `
  /* Let the browser decide paper size; keep a small margin */
  @page { size: auto; margin: 10mm; }

  @media print {
    html, body {
      height: auto !important;
      overflow: visible !important;
      background: white !important;
    }

    /* Use all available width; remove any app-wide max widths */
    .report-root { max-width: none !important; width: 100% !important; margin: 0 !important; padding: 10mm !important; }
    .report-root main,
    .report-root .max-w-\\[1100px\\] { max-width: none !important; width: 100% !important; }

    /* Hide action buttons (print / annexe) in the popup only */
    .print-actions, .print-actions * { display: none !important; }

    /* Kill sticky, scrollbars, and shadows that break printing */
    .sticky { position: static !important; }
    .overflow-x-auto, .overflow-y-auto, .overflow-auto { overflow: visible !important; }
    * { box-shadow: none !important; }

    /* Make grids lay out side-by-side on paper */
    .print-stats-grid {
      display: grid !important;
      grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
      gap: 0.75rem !important;
    }
    .print-two-up {
      display: grid !important;
      grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
      gap: 0.75rem !important;
    }

    /* Tables: multiple per page, repeat header rows, no forced breaks */
    table { page-break-inside: auto; width: 100% !important; }
    thead { display: table-header-group; }
    tfoot { display: table-footer-group; }
    tr, th, td { break-inside: avoid; page-break-inside: avoid; }

    /* Don't force breaks for each matrix chunk */
    .print\\:break-before-page { break-before: auto !important; }

    /* Vertical question labels */
    .vertical-q { height: 10rem; width: 1.75rem; }
  }
`

  const docHTML = `<!doctype html>
<html>
<head>
  <meta charset="utf-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <title>${title.value}</title>
  ${linkTags}
  ${styleTags}
  <style>${helperCSS}</style>
</head>
<body>
  ${root.outerHTML}
</body>
</html>`

  const w = window.open('', '_blank', 'noopener,noreferrer,width=900,height=1200')
  if (!w) {
    const blob = new Blob([docHTML], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    window.open(url, '_blank')
    return
  }
  w.document.open()
  w.document.write(docHTML)
  w.document.close()

  // Optional: auto-trigger print when CSS/fonts are ready
  const tryPrint = () => setTimeout(() => { w.focus(); w.print() }, 50)
  // @ts-ignore
  if ('fonts' in w.document && w.document.fonts?.ready) {
    // @ts-ignore
    w.document.fonts.ready.then(tryPrint).catch(tryPrint)
  } else {
    tryPrint()
  }
}

onMounted(async () => await loadData())
</script>

<style scoped>
/***** Print tweaks when printing inline (not the new window) *****/
@media print {
  section { break-inside: avoid; }
}
/* vertical header using writing-mode (Tailwind utility via arbitrary property is used inline) */
</style>
