import { isTodayParis } from "../../useParisTime";
import { isLiveNow, type Course, type Section } from "./useCoursesLogic";


/**
 * Domain choices (tweak if your backend semantics differ):
 * - "Completed test" := answered >= total_questions  OR completion >= 1
 * - "All questions graded" (per participant) := scored >= answered AND answered > 0
 * - Totals: sum answered and scored across participants
 */
export type GradingStats = {
  totalQuizzes: number;              // number of participants (rows) considered
  finishedQuizzes: number;           // participants who completed the test
  gradedQuizzes: number;             // participants for which all answered Qs are graded
  totalAnsweredQuestions: number;    // sum of answered across participants
  totalGradedQuestions: number;      // sum of scored across participants
};

export function getGradingStats(section: Section): GradingStats {
  const evals = (section as any).evaluations as Record<string, EvalRow> | undefined;
  if (!evals || Object.keys(evals).length === 0) {
    return {
      totalQuizzes: 0,
      finishedQuizzes: 0,
      gradedQuizzes: 0,
      totalAnsweredQuestions: 0,
      totalGradedQuestions: 0,
    };
  }

  const rows = Object.values(evals).filter(isParticipant);

  let totalQuizzes = 0;
  let finishedQuizzes = 0;
  let gradedQuizzes = 0;
  let totalAnsweredQuestions = 0;
  let totalGradedQuestions = 0;

  for (const r of rows) {
    totalQuizzes += 1;

    const tq = toNum(r.total_questions);
    const ta = toNum(r.total_answered);
    const ts = toNum(r.total_scored);
    const comp = toNum(r.completion);

    // Completed if answered >= total questions (or explicit completion flag)
    const completed = (tq > 0 && ta >= tq) || comp >= 1;
    if (completed) finishedQuizzes += 1;

    // All answered questions graded if ts >= ta and ta > 0
    if (ta > 0 && ts >= ta) gradedQuizzes += 1;

    // Totals (cap to avoid weird > totals)
    totalAnsweredQuestions += Math.min(ta, tq || ta);
    totalGradedQuestions += Math.min(ts, tq || ts);
  }

  return {
    totalQuizzes,
    finishedQuizzes,
    gradedQuizzes,
    totalAnsweredQuestions,
    totalGradedQuestions,
  };
}

export type IntervNextAction =
  | { kind: "grade-quizzes"; section: Section; stats: GradingStats }
  | { kind: "host-now" | "host-today" | "upcoming"; section: Section };


function startAnchor(s: Section): number | null {
  // Prefer start_time; fall back to end_time if start_time is missing
  if (s.start_time) return new Date(s.start_time).getTime();
  if (s.end_time) return new Date(s.end_time).getTime();
  return null;
}

export function getInterventionNextAction(course: Course): IntervNextAction | null {
  const list = (course.sections || []) as Section[];
  const now = new Date();

  // 1) Grading: choose the quiz section with the largest pending backlog (answered - graded)
  const pendingForms = list
    .filter((s) => s.type === "FORMULAIRE")
    .map((s) => {
      const stats = getGradingStats(s);
      const backlog = Math.max(0, stats.totalAnsweredQuestions - stats.totalGradedQuestions);
      return { section: s, stats, backlog, start: startAnchor(s) ?? Number.POSITIVE_INFINITY };
    })
    .filter((x) => x.backlog > 0);

  if (pendingForms.length) {
    // Highest backlog first; tie-breaker by earliest start date
    pendingForms.sort((a, b) => (b.backlog - a.backlog) || (a.start - b.start));
    return { kind: "grade-quizzes", section: pendingForms[0].section, stats: pendingForms[0].stats };
  }

  // 2) Live now (presentation)
  const live = list.find((s) => s.type === "PRESENTATION" && isLiveNow(s));
  if (live) return { kind: "host-now", section: live };

  // 3) Today upcoming (presentation) — strictly in the future
  const todayUpcoming = list
    .filter((s) => s.type === "PRESENTATION" && s.start_time)
    .map((s) => ({ s, start: new Date(s.start_time!) }))
    .filter((x) => x.start.getTime() > now.getTime() && isTodayParis(x.start))
    .sort((a, b) => a.start.getTime() - b.start.getTime())[0];
  if (todayUpcoming) return { kind: "host-today", section: todayUpcoming.s };

  // 4) Next future section (any type) — strictly after now, with end_time fallback
  const next = list
    .map((s) => ({ s, t: startAnchor(s) }))
    .filter((x) => x.t !== null && x.t! > now.getTime())
    .sort((a, b) => (a.t! - b.t!))[0];
  if (next) return { kind: "upcoming", section: next.s };

  // Nothing actionable
  return null;
}

