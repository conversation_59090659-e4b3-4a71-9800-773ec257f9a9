<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { storeToRefs } from 'pinia'

// Components
import SiteNavBar from '../../site/SiteNavBar.vue'
import AisyngDialog from '../AisyngDialog2.vue'
import QuizTaker from '../QuizTaker.vue'
import VenueAddress from '../../site/VenueAddress.vue'
import FiltersPills from './FilterPills.vue'
import ResumeBanner from './ResumeBanner.vue'
import CourseCard from './CourseCard.vue'
import AisyngFilterChips from "../../modules/filterChips/AisyngFilterChips.vue";

import {formatDateShort} from "../../modules/clock/clock";

const _clock = getClock();

// Utils kept as-is
import { isOnlineVenue } from './courseUtils.js'
import { onImageError } from '../../useParisTime'
import {Course, listInProgressAndUpcomingSections, Section} from './useCoursesLogic'

// ✅ NEW: Pinia store
import { useMyCoursesStore } from './useRegistrations'
import AisyngButton from "../../modules/button/AisyngButton.vue";
import {getClock} from "./clockSingleton";
import SectionItem from "./SectionItem.vue";

// ---------------------------------
// Dialog state (unchanged)
const quizDialog = ref<InstanceType<typeof AisyngDialog> | null>(null)
const quizParams = ref<{
  sessionSectionId: string | number | null;
  inscriptionId: string | number | null;
  quizName: string | null;
  courseName: string | null;
}>({
  sessionSectionId: null,
  inscriptionId: null,
  quizName: null,
  courseName: null,
})

const filterOptions = [
  { label: 'Toutes', value: 'all' },
  { label: 'À venir', value: 'upcoming' },
  { label: 'En cours', value: 'in-progress' },
  { label: 'Terminées', value: 'done' }
] as const

// Filter (view concern)
const selectedFilter = ref<'all' | 'upcoming' | 'in-progress' | 'done'>('all')

// Store refs
const coursesStore = useMyCoursesStore()
const { loading, error, coursesWithComputed, globalNext } = storeToRefs(coursesStore)

// Derived view data
const filteredCourses = computed(() => {
  if (selectedFilter.value === 'all') return coursesWithComputed.value
  return coursesWithComputed.value.filter((c) => c.status === selectedFilter.value)
})

function openQuizDialog(section: Section, course: Course) {
  quizParams.value = {
    sessionSectionId: section.session_section_id,
    inscriptionId: course.registration?.id ?? (course as any).inscriptionId ?? null,
    courseName: course.course_head?.libellestage,
    quizName: "Test"
  }
  quizDialog.value?.open()
}

onMounted(() => {
  // Lazy-load once per app session
  coursesStore.ensure()
})
</script>

<template>
  <div class="min-h-screen bg-bg px-4">
    <SiteNavBar />
    <div class="max-w-6xl mx-auto pt-6">
      <h1 class="text-2xl md:text-3xl font-bold mb-6 text-center text-[var(--color-amf-primary)]">Mes formations</h1>

      <!-- Filters + Refresh button -->
      <div class="mb-6 flex items-center justify-between gap-4 w-full">
        <AisyngFilterChips
          v-model="selectedFilter"
          :options="filterOptions"
          :multiple="false"
          aria-label="Filtrer mes formations"
        />
        <AisyngButton
            :variant="'outline'"
            :loading="loading"
            @click="coursesStore.refresh()"
        >
          <svg v-if="loading" class="animate-spin h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" opacity="0.25" />
            <path d="M22 12a10 10 0 0 1-10 10" stroke="currentColor" stroke-width="4" stroke-linecap="round" />
          </svg>
          <span>{{ loading ? 'Rafraîchissement…' : 'Rafraîchir' }}</span>
        </AisyngButton>
      </div>

      <!-- Loading -->
      <div v-if="loading" class="space-y-6">
        <div v-for="i in 3" :key="i" class="bg-white rounded-2xl shadow p-6 animate-pulse">
          <div class="h-6 bg-gray-100 rounded w-1/3 mb-4"></div>
          <div class="h-4 bg-gray-100 rounded w-1/2 mb-2"></div>
          <div class="h-4 bg-gray-100 rounded w-1/4"></div>
        </div>
      </div>

      <!-- Error -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-800 rounded-xl p-4">
        {{ error }}
      </div>

      <!-- Empty -->
      <div v-else-if="filteredCourses.length === 0" class="bg-white rounded-xl shadow p-8 text-center text-gray-600">
        Vous n'êtes inscrit à aucune formation pour l'instant.<br />
        <a
          href="/formations"
          class="inline-block mt-4 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-semibold transition"
        >
          Voir le catalogue
        </a>
      </div>

      <!-- Courses -->
      <div v-else class="space-y-8">
        <CourseCard
          v-for="course in filteredCourses"
          :key="course.session_id"
          :course="course"
          :is-online-venue="(head:any) => isOnlineVenue(head)"
          :on-image-error="onImageError"
          @open-quiz="({ section, course }) => openQuizDialog(section, course)"
        >
        </CourseCard>
      </div>
    </div>

    <!-- Dialog -->
    <AisyngDialog ref="quizDialog" :title="'Passer le quiz'">
      <template #title>
        <h2 class="text-lg font-bold"> {{ quizParams.courseName }} </h2>
      </template>
      <QuizTaker
        v-if="quizParams.sessionSectionId && quizParams.inscriptionId"
        :sessionSectionId="quizParams.sessionSectionId"
        :inscriptionId="quizParams.inscriptionId"
        @quizSaved="quizDialog?.close()"
      />
    </AisyngDialog>
  </div>
</template>