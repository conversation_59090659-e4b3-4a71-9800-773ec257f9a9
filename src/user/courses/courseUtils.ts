const TWO_WEEKS_MS = 30 * 24 * 60 * 60 * 1000;
const ONLINE_LIEU_ID = 117;

/** Returns true if the session is “online” based on lieudeformation_id or is_online flag */
export function isOnlineVenue(head?: any) {
  if (!head) return false;
  return head.lieudeformation_id === ONLINE_LIEU_ID || head.is_online === 1;
}

/** Build a compact single-line display string for the venue */
export function getVenueDisplay(head?: any): string {
  if (!head) return '';
  if (isOnlineVenue(head)) return 'AMF Formation en Ligne et en Direct';

  const name = head.nomlieu || head.location_name || head.liblieu || null;
  const addr1 = head.adresse1?.trim();
  const addr2 = head.adresse2?.trim();
  const cityLine = [head.cp, head.ville].filter(Boolean).join(' ');
  const region = head.region?.trim();

  // Compose non-empty parts
  const parts = [name, addr1, addr2, cityLine || null, region].filter(Boolean);
  return parts.join(', ');
}

/** Build a Google Maps search URL for the venue (null if online) */
function getVenueMapsUrl(head?: any): string | null {
  if (!head || isOnlineVenue(head)) return null;
  const qParts = [
    head.nomlieu || head.location_name || head.liblieu,
    head.adresse1,
    [head.cp, head.ville].filter(Boolean).join(' '),
    head.region,
    'France'
  ].filter(Boolean).join(', ');
  return `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(qParts)}`;
}