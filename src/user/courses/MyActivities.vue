<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { storeToRefs } from 'pinia'

// Components
import SiteNavBar from '../../site/SiteNavBar.vue'

import {Course, listInProgressAndUpcomingSections, Section, sortInProgressAndUpcoming} from './useCoursesLogic'

// ✅ NEW: Pinia store
import { useMyCoursesStore } from './useRegistrations'
import {getClock} from "./clockSingleton";
import SectionItem from "./SectionItem.vue";
import SiteFooter from "../../site/SiteFooter.vue";
import {useInterventionsStore} from "./useInterventions";


// Store refs
const myCoursesStore = useMyCoursesStore()
const myInterventionsStore = useInterventionsStore()
const myActivities = computed(() => sortInProgressAndUpcoming([
    ...listInProgressAndUpcomingSections(myCoursesStore.courses),
    ...listInProgressAndUpcomingSections(myInterventionsStore.courses)
]))

const { loading, error } = storeToRefs(myCoursesStore)



onMounted(() => {
  // Lazy-load once per app session
  myCoursesStore.ensure()
  console.log("myActivities", myInterventionsStore.courses)
})
</script>

<template>
  <SiteNavBar />
  <div class="min-h-screen bg-bg px-4">

    <div class="max-w-6xl mx-auto pt-6">
      <h1 class="text-2xl md:text-3xl font-bold mb-6 text-center text-[var(--color-amf-primary)]">
        Mes activités
      </h1>

      <div v-for="a in myActivities" :key="`${a.section.session_section_id}-${a.course.registration}`">
        <SectionItem
            :section="a.section"
            :course="a.course"
            :show-course-title="true"
        />
      </div>

    </div>

  </div>
  <SiteFooter/>
</template>