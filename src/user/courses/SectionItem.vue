<script setup lang="ts">
import {computed, ref} from "vue";
import {can<PERSON><PERSON>n<PERSON><PERSON>, getDateRangeDayStatus, isLiveNow, joinAvailabilityText} from "./useCoursesLogic";

import type { Course, Section } from "./useCoursesLogic";
import { formatDateRange } from "../../modules/clock/clock";

// New UI components
import AisyngButton from "../../modules/button/AisyngButton.vue";
import AisyngChip from "../../modules/chips/AisyngChips.vue";
import {getSessionQuizSummary} from "./useInterventionsLogic";
import {getQuizStats} from "../../services";
import AisyngDialog from "../AisyngDialog2.vue";
import QuizTaker from "../QuizTaker.vue";
import QuizGrader from "../QuizGrader.vue";
import QuizStats from "../QuizStats.vue";

const props = defineProps<{
  section: Section;
  course: Course;
  showCourseTitle: boolean;
}>();

const emit = defineEmits<{
  (e: "open-quiz", payload: { section: Section; course: Course }): void,
  (e: "open-quiz-corrector", payload: { section: Section; course: Course }): void,
  (e: "open-stats", payload: { section: Section; course: Course }): void,
}>();

const backgroundColour = computed(() => {
  if (props.course.course_head?.organisme === "ANDPC") return "bg-green-500";
  if (props.course.course_head?.organisme === "FAF") {
    if (props.course.course_head.codestage?.includes("GEAP"))
      return "bg-orange-500";
    else
      return "bg-blue-500";
  }
  return "bg-white";
});

const quizParams = ref<{
  sessionSectionId: string | number | null;
  inscriptionId: string | number | null;
  quizName: string | null;
  courseName: string | null;
}>({
  sessionSectionId: null,
  inscriptionId: null,
  quizName: null,
  courseName: null,
})

// Dialog state (unchanged)
const quizDialog = ref<InstanceType<typeof AisyngDialog> | null>(null);
const quizCorrectorDialog = ref<InstanceType<typeof AisyngDialog> | null>(null);
const quizStatsDialog = ref<InstanceType<typeof AisyngDialog> | null>(null);

function openQuizDialog() {
  quizParams.value = {
    sessionSectionId: props.section.session_section_id,
    inscriptionId: props.course.registration?.id ?? (props.course as any).inscriptionId ?? null,
    courseName: props.course.course_head?.libellestage,
    quizName: "Test"
  }
  quizDialog.value?.open()
}

function openQuizCorrectorDialog() {
  quizCorrectorDialog.value?.open()
}

function openStatsDialog() {
  quizStatsDialog.value?.open()
}



const min_minutes = computed(() => props.section.evaluation.min_minutes ? parseFloat(props.section.evaluation.min_minutes) : null);
const conn_minutes = computed(() => parseFloat(props.section.evaluation.conn_minutes ?? 0));
const remaining_minutes = computed(() =>
    min_minutes.value ?
        (conn_minutes.value ? min_minutes.value - conn_minutes.value + 1 : min_minutes.value)
        : null
);

const correctionSectionEvalStats = computed(() => {
  if (props.section.type !== "CORRECTION") return null;
  return getSessionQuizSummary(props.section)
});

// ---- Presentation helpers
const dayStatusChipColour: Record<Section["dayStatus"], string> = {
  today: "orange",
  starts: "blue",
  "in-progress": "teal",
  ends: "orange",
  before: "gray",
  after: "gray",
  unknown: "gray",
};

const dayStatusChipText: Record<Section["dayStatus"], string> = {
  today: "Aujourd'hui",
  starts: "Commence aujourd'hui",
  "in-progress": "En cours",
  ends: "Termine aujourd'hui",
  before: "À venir",
  after: "Terminé",
  unknown: "Inconnu",
};

// ---- Computed (avoid repeated calls in template)
const status = computed(() => getDateRangeDayStatus(props.section.start_time, props.section.end_time));
const canOpenQuiz = computed(() => isLiveNow(props.section));
const canZoom = computed(() => canJoinZoom(props.section));
const zoomHelpText = computed(() => joinAvailabilityText(props.section));
const dateRange = computed(() => formatDateRange(props.section.start_time, props.section.end_time));
const franceTzTooltip = computed(() => `${dateRange.value} — heure de France (Europe/Paris)`);
const isParticipation = computed(() => !!props.course.registration);

// ---- Actions
function openZoomInNewTab() {
  if (!canZoom.value || !props.section.zoom_url) return;
  window.open(props.section.zoom_url, "_blank", "noopener,noreferrer");
}
</script>

<template>
  {{course.course_head}}
  <div :class="'mb-4 '" aria-live="polite" role="group">
    <div
      :class="'rounded-2xl border p-4 md:p-5 flex flex-col md:flex-row md:items-start md:justify-between gap-4' +
             ' bg-white shadow-[0_6px_26px_rgba(0,137,145,0.08)]' +
             ' border-[var(--amf-primary,#008891)]/[0.25]' "
    >
      <!-- Left: meta -->
      <div class="flex items-start gap-3 min-w-0 md:flex-1">
        <div class="text-slate-800 min-w-0 w-full">
          <!-- Course tag row -->
          <div class="flex flex-wrap items-center gap-2">
            <AisyngChip
              size="md"
              variant="outline"
              :color="isParticipation ? 'gr' : 'blue'"
              class="max-w-full"
              :title="isParticipation ? 'Vous êtes participant à cette formation.' : 'Vous êtes intervenant à cette formation.'"
            >
              <span v-if="isParticipation">
                P
              </span>
              <span v-else>
                I
              </span>

            </AisyngChip>
            <h3
              class="text-base md:text-base font-bold mt-3 mb-3 text-left text-[var(--color-amf-primary)]"
              :title="section.name"
            >
              {{ course.course_head?.libellestage || course.title }}
            </h3>

          </div>

          <!-- Title -->
          <h3
            class="text-xl md:text-xl font-bold mt-3 mb-3 text-left text-[var(--color-amf-primary)] truncate"
            :title="section.name"
          >
            {{ section.name }}
          </h3>

          <!-- Meta row: dates, type, required, day status -->
          <div class="mt-1 flex flex-wrap items-center gap-2">
            <!-- Date range with France timezone tooltip -->
            <AisyngChip
              size="md"
              variant="outline"
              color="teal"
              :title="franceTzTooltip"
              aria-label="Période — heure de France (Europe/Paris)"
            >
              <template #icon-left><i class="fa fa-calendar" aria-hidden="true"></i></template>
              {{ dateRange }}
            </AisyngChip>

            <AisyngChip
              size="md"
              variant="soft"
              :color="dayStatusChipColour[status]"
              :title="dayStatusChipText[status]"
            >
              <template #icon-left><i class="fa fa-sun" aria-hidden="true"></i></template>
              {{ dayStatusChipText[status] }}
            </AisyngChip>

            <AisyngChip
              v-if = "section.type === 'FORMULAIRE'"
              size="md"
              variant="soft"
              :color="section.evaluation.total_answered < section.evaluation.total_questions ? 'amber' : 'green'"
              title="Nombre de réponses reçues vs. attendues"
            >
              {{section.evaluation.total_answered}} / {{section.evaluation.total_questions}} questions répondues
            </AisyngChip>

            <AisyngChip
              v-if = "section.type === 'FORMULAIRE' && min_minutes"
              size="md"
              variant="soft"
              :color="conn_minutes >= min_minutes ? 'green' : 'amber'"
              :title="conn_minutes >= min_minutes ?
                'Vous avez déjà dépassé le temps minimum requis' :
                (`Vous devez passer au moins ${min_minutes} minutes sur ce test. Vous avez actuellement passé ${conn_minutes.toFixed(0)} minutes et il vous reste ${remaining_minutes.toFixed(0)} minutes à compléter.`)"
            >
              <span v-if ="conn_minutes >= min_minutes">
                Terminé
              </span>
              <span v-else>
                {{remaining_minutes.toFixed(0)}} minutes a faire
              </span>

            </AisyngChip>

            <AisyngChip
              v-if = "section.type === 'CORRECTION'"
              size="md"
              variant="soft"
              :color="correctionSectionEvalStats.totalAnswered >= correctionSectionEvalStats.totalQuestions ? 'green' : 'amber'"
              title="Nombre de réponses reçues vs. attendues"
            >
              <template #icon-left><i class="fa fa-sun" aria-hidden="true"></i></template>
              {{ correctionSectionEvalStats.totalAnswered }} / {{ correctionSectionEvalStats.totalQuestions }}
            </AisyngChip>
            <AisyngChip
              v-if = "section.type === 'CORRECTION'"
              size="md"
              variant="soft"
              :color="correctionSectionEvalStats.totalAnots > 0 ? 'red' : 'green'"
              title="Nombre de réponses nécessitant votre correction. Les autres réponses ont soit déjà été corrigées par vous, soit ce sont des questions à choix corrigées automatiquement."
            >
              <template #icon-left><i class="fa fa-sun" aria-hidden="true"></i></template>
              <span v-if="correctionSectionEvalStats.totalAnots > 0">
                {{ correctionSectionEvalStats.totalAnots }} à corriger
              </span>
              <span v-else>
                Rien à corriger pour le moment
              </span>
            </AisyngChip>

            <AisyngChip
              v-if = "section.type === 'CORRECTION'"
              size="md"
              variant="soft"
              color="amber"
              title="Score moyen sur 20 pour tous les participants ayant répondu à au moins une question. La valeur n’est pas pertinente tant que le test n’est pas terminé."
            >
              {{ (20 * correctionSectionEvalStats.averageRowScore).toFixed(2) }}
            </AisyngChip>

          </div>
        </div>
      </div>

      <!-- Right: Actions (locked width so CTAs align down the page) -->
      <div
        class="flex flex-col items-stretch gap-2 md:items-end md:w-[192px] w-full md:shrink-0"
      >
        <!-- Quiz action -->
        <AisyngButton
          v-if="section.type === 'FORMULAIRE'"
          variant="primary"
          rounded="lg"
          class="min-w-[9.5rem] md:w-[192px] w-full"
          :disabled="!canOpenQuiz"
          :aria-disabled="!canOpenQuiz"
          :title="canOpenQuiz ? 'Passer le quiz' : 'Indisponible'"
          @click="openQuizDialog"
        >
          <i class="fa fa-clipboard-check" aria-hidden="true"></i>
          Passer le quiz
        </AisyngButton>

        <template v-else-if="section.type === 'CORRECTION'">
          <AisyngButton
            variant="primaryBlue"
            rounded="lg"
            class="min-w-[9.5rem] md:w-[192px] w-full"
            :disabled="!canOpenQuiz"
            :aria-disabled="!canOpenQuiz"
            :title="canOpenQuiz ? 'Passer le quiz' : 'Indisponible'"
            @click="openQuizCorrectorDialog"
          >
            <i class="fa fa-clipboard-check" aria-hidden="true"></i>
            Corriger les quiz
          </AisyngButton>

          <AisyngButton
            variant="outline"
            rounded="lg"
            class="min-w-[9.5rem] md:w-[192px] w-full"
            :disabled="!canOpenQuiz"
            :aria-disabled="!canOpenQuiz"
            :title="canOpenQuiz ? 'Stats' : 'Indisponible'"
            @click="openStatsDialog"
          >
            <i class="fa fa-bar-chart" aria-hidden="true"></i>
            Statistiques
          </AisyngButton>
        </template>

        <!-- Zoom action (single button, always opens new tab) -->
        <template v-else-if="section.type === 'PRESENTATION'">
          <AisyngButton
            v-if="section.zoom_url"
            :variant="isParticipation ? 'primary' : 'primaryBlue'"
            rounded="lg"
            class="min-w-[9.5rem] md:w-[192px] w-full"
            :disabled="!canJoinZoom"
            :aria-disabled="!canJoinZoom"
            :title="canJoinZoom ? 'Rejoindre la session Zoom (nouvel onglet)' : zoomHelpText"
            aria-label="Rejoindre la session Zoom"
            @click="openZoomInNewTab"
            :class="{ 'pointer-events-none opacity-50': !canZoom }"
          >
            <i class="fa fa-video-camera" aria-hidden="true"></i>
            Rejoindre
          </AisyngButton>

          <!-- Helper text when Zoom not yet available -->
          <div
            v-if="section.zoom_url && !canZoom"
            class="text-[11px] leading-4 text-gray-500 md:text-right"
            aria-live="polite"
          >
            {{ zoomHelpText }}
          </div>

          <!-- In-person / no Zoom -->
          <button
            v-if="!section.zoom_url"
            class="inline-flex items-center justify-center gap-2 font-medium rounded-lg px-4 py-2 min-w-[9rem] md:w-[192px] w-full bg-gray-100 text-gray-600 border border-transparent cursor-not-allowed"
            :aria-disabled="true"
            title="Session en présentiel"
          >
            <i class="fa fa-ban" aria-hidden="true"></i>
            Session en présentiel
          </button>
        </template>

        <!-- No-action fallback -->
        <button
          v-else
          class="inline-flex items-center justify-center gap-2 font-medium rounded-lg px-4 py-2 min-w-[9rem] md:w-[192px] w-full
                 bg-gray-100 text-gray-600 border border-transparent cursor-not-allowed"
          :aria-disabled="true"
          title="Aucune action disponible"
        >
          <i class="fa fa-minus-circle" aria-hidden="true"></i>
          Aucune action
        </button>
      </div>
    </div>
  </div>

  <!-- Dialog -->
  <AisyngDialog ref="quizDialog" :title="'Passer le quiz'">
    <template #title>
      <h2 class="text-lg font-bold"> {{ quizParams.courseName }} </h2>
    </template>
    <QuizTaker
        v-if="quizParams.sessionSectionId && quizParams.inscriptionId"
        :sessionSectionId="quizParams.sessionSectionId"
        :inscriptionId="quizParams.inscriptionId"
        @quizSaved="quizDialog?.close()"
    />
  </AisyngDialog>

  <AisyngDialog ref="quizCorrectorDialog" :title="'Corriger les quiz'">
    <template #title>
      <h2 class="text-lg font-bold"> {{ quizParams.courseName }} </h2>
    </template>
    <QuizGrader :course="course" :section="section"/>
  </AisyngDialog>

  <AisyngDialog ref="quizStatsDialog" :title="'Statistiques'">
    <template #title>
      <h2 class="text-lg font-bold"> Statistiques de la formation {{ quizParams.courseName }} </h2>
    </template>
    <QuizStats :course="course" :section="section" :passMark="0.5" :matrixColChunk="20" />
  </AisyngDialog>
</template>
