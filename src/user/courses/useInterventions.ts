import {defineStore} from 'pinia'
import {computed, ref} from 'vue'

// Services & utils
import {getUserInterventions} from '../../services'
import {type Course, getCourseTemporalStatus, orderSections, type Section,} from './useCoursesLogic'


export const useInterventionsStore = defineStore('interventions', () => {
  const courses = ref<Course[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const loadedAt = ref<number | null>(null)
  let inFlight: Promise<void> | null = null

  function normalizeSections(sec: any): Section[] {
    // Endpoint returns a dict keyed by session_section_id
    // Convert to array and order consistently
    const arr = Array.isArray(sec) ? sec as Section[] : Object.values(sec || {}) as Section[]
    return orderSections(arr)
  }

  async function _fetch(): Promise<void> {
    loading.value = true
    error.value = null
    try {
      const raw = await getUserInterventions() as any[]
      console.log("raw", raw)

      courses.value = raw.map((c) => {
        return {
          ...c,
          sections: normalizeSections(c.sections)
        } as Course
      })
      loadedAt.value = Date.now()
    } catch (e) {
      console.error('Failed to load interventions:', e)
      error.value = 'Impossible de charger vos interventions pour le moment.'
    } finally {
      loading.value = false
      inFlight = null
    }
  }

  /** Lazy-load once (treat empty as loaded). */
  function ensure(): Promise<void> {
    if (loadedAt.value !== null || loading.value) {
      return inFlight ?? Promise.resolve()
    }
    inFlight = _fetch()
    return inFlight
  }

  /** Manual reload. */
  function refresh(): Promise<void> {
    if (loading.value) return inFlight ?? Promise.resolve()
    inFlight = _fetch()
    return inFlight
  }

  /** Clear on logout / user switch if needed. */
  function invalidate() {
    courses.value = []
    loadedAt.value = null
    error.value = null
  }

  const hasLoaded = computed(() => loadedAt.value !== null)
  const isEmpty = computed(() => hasLoaded.value && courses.value.length === 0)

  const coursesWithComputed = computed(() =>
    courses.value.map((c) => ({
      ...c,
      status: getCourseTemporalStatus(c),
    })),
  )

  function byStatus(status: 'all' | 'upcoming' | 'in-progress' | 'done') {
    if (status === 'all') return coursesWithComputed.value
    return coursesWithComputed.value.filter((c) => c.status === status)
  }

  return {
    // state
    courses, loading, error, loadedAt,

    // lifecycle
    ensure, refresh, invalidate,

    // meta
    hasLoaded, isEmpty,

    // derived
    coursesWithComputed, byStatus,
  }
})
