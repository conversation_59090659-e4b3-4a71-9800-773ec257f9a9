// src/composables/useCourseFiles.ts
import { computed, isRef, unref, type ComputedRef, type Ref } from "vue";

export type MaybeFile =
  | string
  | {
      session_asset_id: string | number;
      // where the file is
      url?: string;
      href?: string;
      path?: string;
      location?: string;

      // display names
      name?: string;
      title?: string;
      file_name?: string;
      filename?: string;

      // mime-ish
      mime?: string;
      mime_type?: string;
      content_type?: string;

      // domain/type (e.g. "Bibliographie", "Certificat")
      type?: string;
      doc_type?: string;
      document_type?: string;
      category?: string;

      // misc
      size?: number;
      bytes?: number;
      id?: string | number;
    };

export type FileKind =
  | "pdf"
  | "doc"
  | "xls"
  | "ppt"
  | "image"
  | "zip"
  | "video"
  | "audio"
  | "txt"
  | "other";

export type NormalizedFile = {
  href: string;
  name: string;
  ext: string;         // e.g. 'pdf'
  kind: FileKind;      // icon/color bucket (NOT the domain doc type)
  size?: number;
  key: string;
  docType?: string;    // e.g. "Bibliographie" | "Certificat" | ...
};

export interface UseCourseFilesOptions {
  /**
   * Any reactive/non-reactive source for the raw files array.
   * (array, Ref, ComputedRef, or a getter function)
   */
  source:
    | MaybeFile[]
    | Ref<MaybeFile[] | undefined | null>
    | ComputedRef<MaybeFile[] | undefined | null>
    | (() => MaybeFile[] | undefined | null);

  /** Sort so Certificat/Bibliographie appear first. Default: true */
  sortByDocType?: boolean;
}

/* ---------------- internal helpers ---------------- */

const getUrl = (f: MaybeFile): string | null =>
  typeof f === "string" ? f : f.url || f.href || f.path || f.location || null;

const getName = (f: MaybeFile, url: string): string => {
  if (typeof f === "string") return decodeURIComponent(url.split("/").pop() || "document");
  const n = f.name || f.title || f.file_name || f.filename;
  if (n) return n;
  const pathPart = decodeURIComponent((url.split("?")[0] || "").split("/").pop() || "");
  return pathPart || "document";
};

const getExt = (s: string): string => {
  const qless = (s || "").split("?")[0];
  const i = qless.lastIndexOf(".");
  return i === -1 ? "" : qless.slice(i + 1).toLowerCase();
};

const mimeToKind = (mime?: string | null): FileKind => {
  const m = (mime || "").toLowerCase();
  if (!m) return "other";
  if (m.includes("pdf")) return "pdf";
  if (m.includes("word") || m.includes("msword") || m.includes("officedocument.word")) return "doc";
  if (m.includes("excel") || m.includes("spreadsheet")) return "xls";
  if (m.includes("powerpoint") || m.includes("presentation")) return "ppt";
  if (m.startsWith("image/")) return "image";
  if (m.startsWith("video/")) return "video";
  if (m.startsWith("audio/")) return "audio";
  if (m.includes("zip") || m.includes("rar") || m.includes("7z")) return "zip";
  if (m.includes("text/")) return "txt";
  return "other";
};

const extToKind = (ext: string): FileKind => {
  if (!ext) return "other";
  if (ext === "pdf") return "pdf";
  if (["doc", "docx", "odt", "rtf"].includes(ext)) return "doc";
  if (["xls", "xlsx", "ods", "csv"].includes(ext)) return "xls";
  if (["ppt", "pptx", "odp"].includes(ext)) return "ppt";
  if (["png", "jpg", "jpeg", "gif", "webp", "tiff", "bmp", "svg"].includes(ext)) return "image";
  if (["zip", "rar", "7z", "tar", "gz"].includes(ext)) return "zip";
  if (["mp4", "mov", "avi", "mkv", "webm"].includes(ext)) return "video";
  if (["mp3", "wav", "aac", "flac", "m4a", "ogg"].includes(ext)) return "audio";
  if (["txt", "md", "json", "xml", "yml", "yaml"].includes(ext)) return "txt";
  return "other";
};

const getDocType = (f: MaybeFile): string | undefined =>
  typeof f === "string"
    ? undefined
    : f.doc_type || f.document_type || f.type || f.category || undefined;

// Certificat → Bibliographie → others
const docTypePriority = (t?: string) => {
  const v = (t || "").toLowerCase();
  if (v === "certificat") return 0;
  if (v === "bibliographie") return 1;
  return 2;
};

/* ---------------- public helpers ---------------- */

export const formatBytes = (bytes?: number): string | undefined => {
  if (bytes == null || Number.isNaN(bytes)) return undefined;
  if (bytes < 1024) return `${bytes} o`;
  const units = ["Ko", "Mo", "Go", "To"];
  let i = -1;
  let b = bytes;
  do {
    b = b / 1024;
    i++;
  } while (b >= 1024 && i < units.length - 1);
  return `${b.toFixed(b >= 10 ? 0 : 1)} ${units[i]}`;
};

export const isCertificat = (f: NormalizedFile) => f.docType?.toLowerCase() === "certificat";
export const isBibliographie = (f: NormalizedFile) => f.docType?.toLowerCase() === "bibliographie";

/**
 * Main composable
 */
export function useCourseFiles(opts: UseCourseFilesOptions) {
  const sourceRef = computed<MaybeFile[] | undefined | null>(() => {
    const s = opts.source;
    if (typeof s === "function") return (s as () => MaybeFile[] | undefined | null)();
    if (isRef(s)) return unref(s);
    return s as MaybeFile[] | undefined | null;
  });

  const filesList = computed<NormalizedFile[]>(() => {
    const raw = sourceRef.value;
    if (!Array.isArray(raw) || raw.length === 0) return [];
    const list = raw
      .map((f, idx) => {
        const url = getUrl(f);
        if (!url) return null;
        const name = getName(f, url);
        const ext = getExt(name || url);
        const mime =
          typeof f === "string" ? undefined : f.mime || f.mime_type || f.content_type || undefined;
        const kind = mime ? mimeToKind(mime) : extToKind(ext);
        const size = typeof f === "string" ? undefined : f.size ?? (f as any).bytes;
        const key =
          (typeof f !== "string" && (f.id?.toString?.() || "")) || `${url}::${idx}`;
        const docType = getDocType(f);
        return { href: url, name, ext: ext || kind, kind, size, key, docType, session_asset_id: f.session_asset_id,
        } as NormalizedFile;
      })
      .filter(Boolean) as NormalizedFile[];

    return (opts.sortByDocType ?? true)
      ? list.sort((a, b) => docTypePriority(a.docType) - docTypePriority(b.docType))
      : list;
  });

  return {
    filesList,
    formatBytes,
    isCertificat,
    isBibliographie,
  };
}
