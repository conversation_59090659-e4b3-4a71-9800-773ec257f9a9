<script setup lang="ts">
import { formatTimeHHMM, formatDateShort } from "../../useParisTime";
import type { Course, NextAction } from "./useCoursesLogic";
import AisyngButton from "../../modules/button/AisyngButton.vue";
import AisyngChip from "../../modules/chips/AisyngChips.vue";

const props = defineProps<{
  globalNext: { course: Course; next: NextAction } | null;
}>();

const emit = defineEmits<{
  (e: "take-quiz", payload: { section: any; course: Course }): void;
}>();
</script>

<template>
  <div v-if="globalNext" class="mb-6" aria-live="polite" role="status">
    <div
      class="rounded-2xl border p-4 md:p-5 flex flex-col md:flex-row md:items-center md:justify-between gap-4
             bg-white shadow-[0_6px_26px_rgba(0,137,145,0.08)]
             border-[var(--amf-primary,#008891)]/[0.25]"
    >
      <!-- Left: icon + text -->
      <div class="flex items-start gap-3">
        <!-- Accent icon -->
        <div
          class="shrink-0 mt-0.5 h-10 w-10 rounded-xl flex items-center justify-center
                 bg-[var(--amf-primary,#008891)]/10 text-[var(--amf-primary-dark,#005f73)]"
          aria-hidden="true"
        >
          <i class="fa fa-play"></i>
        </div>

        <div class="text-slate-800">

          <AisyngChip size="sm" variant="soft" color="teal"
            :title="globalNext.course.course_head?.libellestage || globalNext.course.title"
          >
            {{ globalNext.course.course_head?.libellestage || globalNext.course.title }}
          </AisyngChip>

          <div class="mt-1.5 text-base md:text-lg font-medium leading-snug text-slate-900">
            <template v-if="globalNext.next.kind === 'take-quiz'">
              Passer le quiz « {{ globalNext.next.section.name }} »
            </template>

            <template v-else-if="globalNext.next.kind === 'join-now'">
              Rejoindre la session en direct — « {{ globalNext.next.section.name }} »
            </template>

            <template v-else-if="globalNext.next.kind === 'join-today' || globalNext.next.kind === 'join-at'">
              Prochaine session aujourd'hui à {{ formatTimeHHMM(globalNext.next.section.start_time) }} — « {{ globalNext.next.section.name }} »
            </template>

            <template v-else>
              Prochaine étape : « {{ globalNext.next.section.name }} »
              le {{ formatDateShort(globalNext.next.section.start_time) }}
              à {{ formatTimeHHMM(globalNext.next.section.start_time) }}
            </template>
          </div>

          <!-- Optional meta row (date/time chip) -->
          <div v-if="globalNext.next.section?.start_time" class="mt-2 flex flex-wrap items-center gap-2">
            <AisyngChip size="sm" variant="outline" color="teal">
              <template #icon-left><i class="fa fa-calendar"></i></template>
              {{ formatDateShort(globalNext.next.section.start_time) }} •
              {{ formatTimeHHMM(globalNext.next.section.start_time) }}
            </AisyngChip>
          </div>
        </div>
      </div>

      <!-- Right: CTAs -->
      <div class="flex items-center gap-3">
        <!-- Take quiz -->
        <AisyngButton
          v-if="globalNext.next.kind === 'take-quiz'"
          variant="primary"
          rounded="lg"
          @click="emit('take-quiz', { section: globalNext.next.section, course: globalNext.course })"
          class="min-w-[9.5rem]"
        >
          <i class="fa fa-clipboard-check"></i>
          Passer le quiz
        </AisyngButton>

        <!-- Join now / today (link styled to match AisyngButton 'primary') -->
        <a
          v-else-if="globalNext.next.section.zoom_url && (globalNext.next.kind === 'join-now' || globalNext.next.kind === 'join-today')"
          :href="globalNext.next.section.zoom_url"
          target="_blank"
          rel="noopener"
          class="inline-flex items-center justify-center gap-2 font-medium rounded-lg
                 px-4 py-2 min-w-[8.5rem]
                 bg-[var(--amf-primary,#008891)] text-white border border-[var(--amf-primary,#008891)]
                 hover:bg-[var(--amf-primary-dark,#005f73)]
                 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-1 focus-visible:ring-teal-300
                 transition-all duration-150 active:scale-95"
        >
          <i class="fa fa-video-camera"></i>
          Rejoindre
        </a>

        <!-- Disabled / unavailable -->
        <button
          v-else
          class="inline-flex items-center justify-center gap-2 font-medium rounded-lg
                 px-4 py-2 min-w-[9rem]
                 bg-gray-100 text-gray-600 border border-transparent
                 cursor-not-allowed"
          :aria-disabled="true"
          :title="globalNext.next.section ? 'Indisponible' : ''"
        >
          <i class="fa fa-ban"></i>
          Indisponible
        </button>
      </div>
    </div>
  </div>
</template>
