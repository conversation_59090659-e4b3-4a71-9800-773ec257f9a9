
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// Keep your existing types/utilities
import { getUserCourses } from '../../services'
import { loadStageImage } from '../../formation'
import {
  getCourseTemporalStatus,
  courseProgress,
  type Course, orderSections,
} from './useCoursesLogic'

export const useMyCoursesStore = defineStore('my-courses', () => {
  const courses = ref<Course[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const loadedAt = ref<number | null>(null)

  // Prevent duplicate concurrent fetches
  let inFlight: Promise<void> | null = null

  async function _fetch(): Promise<void> {
    loading.value = true
    error.value = null
    try {
      const result: Course[] = await getUserCourses()
      console.log("result", result)

      // Normalize sections order
      result.forEach((c) => (c.sections = orderSections(c.sections)))

      // Preload images (non-blocking errors)
      await Promise.all(
        result.map(async (course) => {
          const url = course?.course_head?.image_url
          if (url) {
            try {
              course.course_head.image = await loadStageImage(url)
            } catch {
              /* ignore; components use fallback */
            }
          }
        }),
      )

      courses.value = result
      loadedAt.value = Date.now()
    } catch (e) {
      console.error('Failed to load courses:', e)
      error.value = 'Impossible de charger vos formations pour le moment.'
    } finally {
      loading.value = false
      inFlight = null
    }
  }

  /** Ensure courses are loaded once (lazy). */
  function ensure(): Promise<void> {
    if (loadedAt.value !== null || loading.value) {
      return inFlight ?? Promise.resolve()
    }
    inFlight = _fetch()
    return inFlight
  }

  /** Force refresh, de-duped by inFlight guard. */
  function refresh(): Promise<void> {
    if (loading.value) return inFlight ?? Promise.resolve()
    inFlight = _fetch()
    return inFlight
  }

  const hasLoaded = computed(() => loadedAt.value !== null)
  const isEmpty = computed(() => hasLoaded.value && courses.value.length === 0)

  // View helpers (derived data)
  const coursesWithComputed = computed(() =>
    courses.value.map((c) => ({
      ...c,
      temporalStatus: getCourseTemporalStatus(c),
      _progress: courseProgress(c),
    })),
  )


  function byTemporalStatus(status: 'all' | 'upcoming' | 'in-progress' | 'done') {
    if (status === 'all') return coursesWithComputed.value
    return coursesWithComputed.value.filter((c) => c.temporalStatus === status)
  }

  function invalidate() {
      courses.value = []
      loadedAt.value = null
      error.value = null
    }

  return {
    // state
    courses,
    loading,
    error,
    loadedAt,

    // lifecycle
    ensure,
    refresh,
    invalidate,

    // meta
    hasLoadedCourses: hasLoaded,
    isEmpty,

    // getters/derived
    coursesWithComputed,
    byStatus: byTemporalStatus,
  }
})

