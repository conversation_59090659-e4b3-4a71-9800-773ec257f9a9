// useCoursesParis.ts
// One module: Paris time utils + course logic (server-synced via clock singleton)

import { getClock } from "./clockSingleton";
import {
  formatDateFull,
  isSameDayParis,
  parseAsParis,
} from "../../modules/clock/clock";
import {isTodayParis} from "../../useParisTime";                    // your singleton getter


const _clock = getClock();
// ---------- Shared types ----------
export type AnyObj = Record<string, any>;

export interface Section extends AnyObj {
  session_section_id: string | number;
  name?: string;
  required?: boolean;
  type?: "FORMULAIRE" | "PRESENTATION" | string;
  start_time?: string | null;
  end_time?: string | null;
  zoom_url?: string | null;
  temporalStatus?: "done" | "in-progress" | "upcoming" | "unknown";
  dayStatus?: "today" | "starts" | "in-progress" | "ends" | "before" | "after" | "unknown";
  evaluation?: {
    final_passed?: boolean | number | null;
    quiz_passed?: boolean | null;
    final_completion?: number | null;
    quiz_completion?: number | null;
    total_questions?: number | null;
    total_answered?: number | null;
    total_score?: number | null;
  } | null;
}

export interface Course extends AnyObj {
  session_id: string | number;
  start_date?: string | null;
  end_date?: string | null;
  title?: string | null;
  desc?: string | null;
  course_head?: AnyObj;
  sections?: Section[];
  registration?: { id: string | number };
  intervention?: { id: string | number; nature: string };
  participants?: any;
  temporalStatus?: "done" | "in-progress" | "upcoming" | "unknown";
}

export function toNum(v: unknown): number {
  if (v === null || v === undefined || v === "") return 0;
  const n = Number(v);
  return Number.isFinite(n) ? n : 0;
}

// ---------- Cached Paris formatters ----------


// ---------- Core Paris-time helpers (singleton-based) ----------


export function orderSections(sections: any): any[] {
  const arr: any[] = Array.isArray(sections) ? [...sections] : Object.values(sections || {});
  type Decorated = { s:any; idx:number; time:number; bias:number };
  const decorated: Decorated[] = arr.map((s, idx) => {
    const hasStart = !!s?.start_time;
    const start = hasStart ? parseAsParis(s.start_time) : null;
    const end   = s?.end_time ? parseAsParis(s.end_time) : null;
    const time  = (start?.getTime() ?? end?.getTime() ?? Number.POSITIVE_INFINITY);
    const bias  = hasStart ? 1 : (end != null ? 0 : 2); // end-only before start-at-same-time
    return { s, idx, time, bias };
  });
  decorated.sort((a,b) => a.time - b.time || a.bias - b.bias || a.idx - b.idx);
  return decorated.map(d => d.s);
}

export function onImageError(e: Event) {
  const t = e.target as HTMLImageElement;
  t.src = "/images/course_image.jpg";
}

// ---------- Course/business logic (Paris + server-synced now) ----------

export function getNormalizedTimes(startTime: Date | string | null, endTime: Date | string | null): { startAsDate: Date | null; endAsDate: Date | null } {
  if (!startTime && !endTime) return { startAsDate: null, endAsDate: null };

  let start = startTime instanceof Date ? startTime : parseAsParis(startTime);
  let end = endTime instanceof Date ? endTime : parseAsParis(endTime);

  if (!start && end) {
    start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
  }
  if (start && !end) {
    end = new Date(start.getTime() + 7 * 24 * 60 * 60 * 1000);
  }

  return { startAsDate:start, endAsDate:end };
}

export function getDateRangeTemporalStatus(startDate: Date | string | null, endDate: Date | string | null): Course["temporalStatus"] {
  const nowParis = new Date(_clock.now());

  const { startAsDate, endAsDate } = getNormalizedTimes(startDate, endDate);

  if (endAsDate && endAsDate < nowParis) return "done";
  if (startAsDate && startAsDate > nowParis) return "upcoming";
  return "in-progress";
}

export function getDateRangeDayStatus(startDate: Date | string | null, endDate: Date | string | null): Section["dayStatus"] {
  const nowParis = new Date(_clock.now());

  const { startAsDate, endAsDate } = getNormalizedTimes(startDate, endDate);

  if (isSameDayParis(startAsDate, endAsDate) && isTodayParis(_clock, startAsDate)) return "today";
  if (isSameDayParis(startAsDate, nowParis)) return "starts";
  if (isSameDayParis(endAsDate, nowParis)) return "ends";
  if (nowParis >= startAsDate! && nowParis < endAsDate!) return "in-progress";
  if (nowParis < startAsDate!) return "before";
  if (nowParis > endAsDate!) return "after";
  return "unknown";
}

export function getCourseTemporalStatus(course: Course): Course["temporalStatus"] {
  return getDateRangeTemporalStatus(course.start_date, course.end_date);
}

export function getSectionTemporalStatus(section: Section): Section["temporalStatus"] {
  return getDateRangeTemporalStatus(section.start_time, section.end_time);
}

export function sectionCompleted(section: Section): boolean {
  if (section.type === "FORMULAIRE") {
    const ev = section.evaluation || {};
    return Boolean(ev.final_passed || ev.quiz_passed);
  }
  if (section.type === "PRESENTATION") {
    const ev = section.evaluation || {};
    return Number(ev.final_passed) === 1;
  }
  return false;
}

export function listInProgressAndUpcomingSections(
  courses: Course[]
): Array<{ section: Section; course: Course }> {
  type Row = { section: Section; course: Course; bias: number; endAt: number };

  const now = _clock.now();
  const rows: Row[] = [];

  for (const course of courses || []) {
    for (const section of (course.sections || []) as Section[]) {
      const { startAsDate, endAsDate } = getNormalizedTimes(section.start_time ?? null, section.end_time ?? null);

      // Skip if both dates are missing (normalization returns nulls)
      if (!startAsDate || !endAsDate) continue;

      const inProgress = now >= startAsDate && now < endAsDate;
      const upcoming = startAsDate > now;

      if (!inProgress && !upcoming) continue;

      rows.push({
        section,
        course,
        bias: inProgress ? 0 : 1,               // in-progress first, upcoming last
        endAt: endAsDate.getTime(),
      });
    }
  }
  rows.sort((a, b) => a.bias - b.bias || a.endAt - b.endAt);

  return rows.map(({ section, course }) => ({ section, course }));
}


export function isLiveNow(section: Section): boolean {
  const now = _clock.now();
  const { startAsDate, endAsDate } = getNormalizedTimes(section.start_time, section.end_time);
  if (!startAsDate || !endAsDate) return false;
  return now >= startAsDate && now < endAsDate;
}

export function canJoinZoom(
  section: { start_time?: string | null; end_time?: string | null },
  minutesBefore = 30,
  minutesAfter = 15
): boolean {
  const now = _clock.now();
  const { startAsDate, endAsDate } = getNormalizedTimes(section.start_time, section.end_time);

  if (!startAsDate && !endAsDate) return true;

  const openAt = new Date(startAsDate.getTime() - minutesBefore * 60_000);
  const closeAt = new Date(endAsDate.getTime() + minutesAfter * 60_000);

  console.log("canJoinZoom", now, openAt, closeAt, now >= openAt && now <= closeAt);

  return now >= openAt && now <= closeAt;
}

export function joinAvailabilityText(
  section: { start_time?: Date | string | null, end_time?: Date | string | null },
  minutesBefore = 30,
  minutesAfter = 15
): string {
  const now = _clock.now();
  const { startAsDate, endAsDate } = getNormalizedTimes(section.start_time, section.end_time);

  if (!startAsDate && !endAsDate) return `Indisponible`;

  const openAt = new Date(startAsDate.getTime() - minutesBefore * 60_000);
  const closeAt = new Date(endAsDate.getTime() + minutesAfter * 60_000);

  const hhmm = formatDateFull(openAt);
  if (now >= openAt && now <= closeAt) return "Disponible";
  if (now < openAt) return `Disponible à partir de ${hhmm}`;
  if (now > closeAt) return "Fermé";
  return `Indisponible`;
}

export function courseProgress(course: Course) {
  const list = (course.sections || []) as Section[];
  const total = list.length || 0;
  const completed = list.filter(sectionCompleted).length;
  return { completed, total, ratio: total === 0 ? 0 : Math.round((completed / total) * 100) };
}

export type NextAction =
  | { kind: "join-now"; section: Section }
  | { kind: "join-today" | "join-at"; section: Section }
  | { kind: "take-quiz"; section: Section }
  | { kind: "upcoming"; section: Section };


export function sortInProgressAndUpcoming(
  items: Array<{ section: Section; course: Course }>
): Array<{ section: Section; course: Course }> {
  const now = _clock.now();

  type Row = { section: Section; course: Course; bias: number; endAt: number };

  const rows: Row[] = items.map(({ section, course }) => {
    const { startAsDate, endAsDate } = getNormalizedTimes(section.start_time ?? null, section.end_time ?? null);

    const inProgress = startAsDate && endAsDate && now >= startAsDate && now < endAsDate;
    const bias = inProgress ? 0 : 1;
    const endAt = endAsDate?.getTime() ?? Number.POSITIVE_INFINITY;

    return { section, course, bias, endAt };
  });

  rows.sort((a, b) => a.bias - b.bias || a.endAt - b.endAt);
  return rows.map(({ section, course }) => ({ section, course }));
}
