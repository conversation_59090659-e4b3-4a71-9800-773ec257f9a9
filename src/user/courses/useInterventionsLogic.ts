import {Course, getNormalizedTimes, Section, toNum} from "./useCoursesLogic";
import {getClock} from "./clockSingleton";

const _clock = getClock();


// Raw row as it comes from backend (numbers often as strings)
type EvalRow = {
  total_questions: number | string | null;
  total_answered: number | string | null;
  total_scored: number | string | null;
  participant?: number | boolean | null;
  completion?: number | string | null; // 1.0 if fully completed (when present)
  passed?: number | boolean | null;
  anots: number | string | null;
};

function isParticipant(r: EvalRow): boolean {
  // Default to true if field missing; otherwise treat 1/true as participant
  if (r.participant === undefined || r.participant === null) return true;
  return r.participant === 1 || r.participant === true;
}


export const createCorrectionSectionFromFormulaire = (s: Section): Section | null => {
  if (s.type !== "FORMULAIRE") return null;
  const { startAsDate, endAsDate } = getNormalizedTimes(s.start_time ?? null, s.end_time ?? null);
  // We need an end to anchor the correction start. Normalization ensures one exists
  // if there was at least a start OR end. If both were missing, skip.
  if (!startAsDate && !endAsDate) return null;

  const corrStart = startAsDate!; // normalized end; guaranteed if at least one bound existed
  const corrEnd = new Date(corrStart.getTime() + correctionDays * 24 * 60 * 60 * 1000);

  return {
    ...s,
    type: "CORRECTION",
    start_time: corrStart.toISOString(),
    end_time: corrEnd.toISOString(),
    // optional: ensure it's clearly not a live Zoom thing
    zoom_url: null,
    // you may also want to reset evaluation fields for correction view
  }
}

/**
 * List in-progress & upcoming items where:
 *  - PRESENTATION sections are returned as-is
 *  - FORMULAIRE sections are turned into CORRECTION sections:
 *      start_time = original end_time (normalized if missing)
 *      end_time   = start_time + correctionWindowDays
 *      type       = "CORRECTION"
 *
 * Sorted: in-progress first, then by soonest end.
 */
export function listInProgressAndUpcomingWithCorrections(
  courses: Course[],
  opts?: {
    /** Decide if a FORMULAIRE needs a correction window (default: not passed) */
    needsCorrection?: (section: Section, course: Course) => boolean;
    /** Length of correction window in days (default 14) */
    correctionWindowDays?: number;
  }
): Array<{ section: Section; course: Course }> {
  type Row = { section: Section; course: Course; bias: number; endAt: number };

  const needsCorrection =
    opts?.needsCorrection ??
    ((s) => {
      // Example default rule: require correction if quiz not passed
      // You can swap this for your own signal/flag.
      return true;
      const ev = s.evaluation || {};
      return !ev.quiz_passed && !ev.final_passed;
    });

  const correctionDays = opts?.correctionWindowDays ?? 14;

  const now = _clock.now();
  const rows: Row[] = [];

  const toMillis = (d: Date) => d.getTime();

  for (const course of courses || []) {
    for (const raw of (course.sections || []) as Section[]) {
      if (raw.type === "PRESENTATION") {
        const { startAsDate, endAsDate } = getNormalizedTimes(raw.start_time ?? null, raw.end_time ?? null);
        if (!startAsDate || !endAsDate) continue;

        const inProgress = now >= startAsDate && now < endAsDate;
        const upcoming = startAsDate > now;
        if (!inProgress && !upcoming) continue;

        rows.push({
          section: raw,
          course,
          bias: inProgress ? 0 : 1,
          endAt: toMillis(endAsDate),
        });
        continue;
      }

      if (raw.type === "FORMULAIRE") {
        if (!needsCorrection(raw, course)) continue; // discard quizzes that don't need correction
        const correction = createCorrectionSectionFromFormulaire(raw);
        if (!correction) continue;

        const { startAsDate, endAsDate } = getNormalizedTimes(correction.start_time, correction.end_time);
        if (!startAsDate || !endAsDate) continue;

        const inProgress = now >= startAsDate && now < endAsDate;
        const upcoming = startAsDate > now;
        if (!inProgress && !upcoming) continue;

        rows.push({
          section: correction,
          course,
          bias: inProgress ? 0 : 1,
          endAt: toMillis(endAsDate),
        });
      }
      // Any other section types are ignored for this list
    }
  }

  rows.sort((a, b) => a.bias - b.bias || a.endAt - b.endAt);
  return rows.map(({ section, course }) => ({ section, course }));
}


/**
 * Domain choices (tweak if your backend semantics differ):
 * - "Completed test" := answered >= total_questions  OR completion >= 1
 * - "All questions graded" (per participant) := scored >= answered AND answered > 0
 * - Totals: sum answered and scored across participants
 */
export type GradingStats = {
  totalQuizzes: number;              // number of participants (rows) considered
  finishedQuizzes: number;           // participants who completed the test
  gradedQuizzes: number;             // participants for which all answered Qs are graded
  totalAnsweredQuestions: number;    // sum of answered across participants
  totalGradedQuestions: number;      // sum of scored across participants
};

export function getGradingStats(section: Section): GradingStats {
  const evals = (section as any).evaluations as Record<string, EvalRow> | undefined;
  if (!evals || Object.keys(evals).length === 0) {
    return {
      totalQuizzes: 0,
      finishedQuizzes: 0,
      gradedQuizzes: 0,
      totalAnsweredQuestions: 0,
      totalGradedQuestions: 0,
    };
  }

  const rows = Object.values(evals).filter(isParticipant);

  let totalQuizzes = 0;
  let finishedQuizzes = 0;
  let gradedQuizzes = 0;
  let totalAnsweredQuestions = 0;
  let totalGradedQuestions = 0;

  for (const r of rows) {
    totalQuizzes += 1;

    const tq = toNum(r.total_questions);
    const ta = toNum(r.total_answered);
    const ts = toNum(r.total_scored);
    const comp = toNum(r.completion);

    // Completed if answered >= total questions (or explicit completion flag)
    const completed = (tq > 0 && ta >= tq) || comp >= 1;
    if (completed) finishedQuizzes += 1;

    // All answered questions graded if ts >= ta and ta > 0
    if (ta > 0 && ts >= ta) gradedQuizzes += 1;

    // Totals (cap to avoid weird > totals)
    totalAnsweredQuestions += Math.min(ta, tq || ta);
    totalGradedQuestions += Math.min(ts, tq || ts);
  }

  return {
    totalQuizzes,
    finishedQuizzes,
    gradedQuizzes,
    totalAnsweredQuestions,
    totalGradedQuestions,
  };
}

export function getSessionQuizSummary(section: Section): {
  totalQuestions: number;
  totalAnswered: number;
  totalGraded: number;
  totalAnots: number;
  averageRowScore: number; // 0..1 if score is per-question points; 0 if no eligible rows
  rowsCountedForAverage: number;
} {
  const evals = (section as any).evaluations as Record<
    string,
    EvalRow & { total_score?: number | string | null }
  > | undefined;

  if (!evals || Object.keys(evals).length === 0) {
    return {
      totalQuestions: 0,
      totalAnswered: 0,
      totalGraded: 0,
      averageRowScore: 0,
      rowsCountedForAverage: 0,
    };
  }

  const rows = Object.values(evals).filter(isParticipant);

  let totalQuestions = 0;
  let totalAnswered = 0;
  let totalGraded = 0;
  let totalAnots = 0

  let sumOfRowAverages = 0;
  let rowsCountedForAverage = 0;

  for (const r of rows) {
    const tq = toNum((r as any).total_questions);
    const ta = toNum((r as any).total_answered);
    const ts = toNum((r as any).total_scored); // number of questions graded
    const score = toNum((r as any).total_score);
    const anots = toNum((r as any).anots); // points scored (may be null/absent)

    // Totals (cap answered/graded by total_questions when available)
    totalQuestions += Math.max(0, tq);
    totalAnswered += Math.min(Math.max(0, ta), tq || ta || 0);
    totalGraded += Math.min(Math.max(0, ts), tq || ts || 0);
    totalAnots += Math.min(Math.max(0, anots) || 0);

    // Average-of-averages:
    // include only if score > 0 and tq > 0
    if (score > 0 && tq > 0) {
      sumOfRowAverages += score / tq;
      rowsCountedForAverage += 1;
    }
  }

  const averageRowScore =
    rowsCountedForAverage > 0 ? sumOfRowAverages / rowsCountedForAverage : 0;

  return {
    totalQuestions,
    totalAnswered,
    totalGraded,
    totalAnots,
    averageRowScore,
    rowsCountedForAverage,
  };
}
