<script setup>
const props = defineProps({
  course: { type: Object, required: true }
});
const isRegistered = !!props.course.registration;

function formatDate(dateStr) {
  if (!dateStr) return '';
  return new Intl.DateTimeFormat("fr-FR", { year: "numeric", month: "short", day: "numeric" })
    .format(new Date(dateStr));
}
function formatTime(dateStr) {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleTimeString('fr-FR', { hour: "2-digit", minute: "2-digit" });
}
function getDuration(start, end) {
  if (!start || !end) return '';
  const d1 = new Date(start), d2 = new Date(end);
  return Math.round((d2-d1)/1000/60);
}
</script>

<template>
  <div
    class="amf-bg-card amf-shadow amf-rounded flex flex-col group transition hover:scale-[1.017] hover:amf-shadow"
    style="min-width:265px;max-width:330px;"
  >
    <!-- Course image -->
    <div class="relative">
      <img
        :src="course.cover || '/images/course_image.jpg'"
        class="w-full h-36 object-cover"
        :alt="course.course_head.libellestage || 'Formation'"
      />
      <span
        v-if="course.status"
        class="absolute top-2 left-2 px-2 py-1 rounded text-xs font-bold text-white"
        :class="{
          'bg-green-600': course.status === 'done',
          'bg-blue-600': course.status === 'in-progress',
          'bg-gray-500': course.status !== 'done' && course.status !== 'in-progress'
        }"
      >
        {{ course.status === 'done' ? "Terminé"
          : course.status === 'in-progress' ? "En cours" : "À venir" }}
      </span>
      <span
        v-if="isRegistered"
        class="absolute top-2 right-2 bg-yellow-400 text-black font-semibold text-xs rounded px-2 py-1 shadow"
      >Inscrit</span>
    </div>
    <!-- Main content -->
    <div class="flex-1 flex flex-col px-4 py-3">
      <h3 class="text-lg font-extrabold amf-text-primary-dark mb-1 leading-snug truncate group-hover:amf-text-primary transition">
        {{ course.course_head.libellestage || course.course_head.name }}
      </h3>
      <div class="text-gray-600 text-sm flex items-center gap-1 mb-2 truncate">
        <i class="fa fa-map-marker-alt amf-text-primary text-xs"></i>
        <span class="truncate">{{ course.course_head.nomlieu }}, {{ course.course_head.ville }}</span>
      </div>
      <div class="flex items-center gap-2 text-xs text-gray-500 mb-1">
        <i class="fa fa-calendar"></i>
        <span>{{ formatDate(course.start_date) }}</span>
        <span v-if="course.start_date !== course.end_date">
          – {{ formatDate(course.end_date) }}
        </span>
      </div>
      <div class="flex items-center gap-2 text-xs text-gray-500 mb-2">
        <i class="fa fa-clock"></i>
        <span>
          {{ formatTime(course.start_date) }} - {{ formatTime(course.end_date) }}
          <span class="ml-1 text-gray-400">({{ getDuration(course.start_date, course.end_date) }} min)</span>
        </span>
      </div>
      <div class="text-sm text-gray-700 mb-2 line-clamp-3">
        {{ course.description || "Aucune description." }}
      </div>
      <div class="flex flex-wrap gap-2 mt-auto">
        <span
          v-if="course.course_head.organisme"
          class="bg-blue-100 text-blue-900 text-[0.78rem] font-semibold px-2 py-0.5 rounded"
        >{{ course.course_head.organisme }}</span>
        <span
          v-if="course.course_head.session_type"
          class="amf-bg-primary-light amf-text-primary-dark text-[0.78rem] font-semibold px-2 py-0.5 rounded"
        >{{ course.course_head.session_type }}</span>
      </div>
    </div>
    <!-- CTA Footer -->
    <div class="flex justify-end gap-2 p-3 pt-0">
      <slot name="cta" :course="course" :isRegistered="isRegistered">
        <a
          :href="`/formation/${course.session_id}`"
          class="amf-bg-primary hover:amf-bg-primary-light text-white font-bold rounded px-4 py-2 text-sm transition"
        >
          Voir la formation
        </a>
      </slot>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
