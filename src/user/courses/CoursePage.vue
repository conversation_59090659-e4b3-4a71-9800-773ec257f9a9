<script setup>
import { computed } from "vue";

// Props: expects the full course object as described above
const props = defineProps({
  course: { type: Object, required: true }
});

// Registered or not?
const isRegistered = computed(() => !!props.course.registration);

// Format dates
function formatDate(dateStr) {
  if (!dateStr) return '';
  return new Intl.DateTimeFormat("fr-FR", {
    year: "numeric", month: "2-digit", day: "2-digit"
  }).format(new Date(dateStr));
}
function formatTime(dateStr) {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleTimeString('fr-FR', {
    hour: "2-digit", minute: "2-digit"
  });
}

// Duration in minutes
function getDuration(start, end) {
  if (!start || !end) return '';
  const d1 = new Date(start), d2 = new Date(end);
  return Math.round((d2-d1)/1000/60);
}

// Registration CTA (you’d replace with your actual logic)
function register() {
  alert("TODO: Implement registration logic here.");
}

// Show sections as an array, sorted by start_time
const sectionList = computed(() => {
  if (!props.course.sections) return [];
  return Object.values(props.course.sections).sort(
    (a, b) => new Date(a.start_time) - new Date(b.start_time)
  );
});
</script>

<template>
  <div class="max-w-5xl mx-auto p-4 md:p-8">
    <!-- DEADLINE -->
    <div v-if="course.deadline" class="mb-2 flex items-center text-sm text-gray-500 gap-2">
      <i class="fa fa-calendar"></i>
      <span>Date limite d'inscription : {{ formatDate(course.deadline) }}</span>
    </div>

    <!-- TITLE & TAGS -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-3">
      <h1 class="text-2xl md:text-3xl font-extrabold text-accent mb-2 md:mb-0">
        {{ course.course_head.libellestage || course.course_head.name }}
      </h1>
      <div class="flex flex-wrap gap-2">
        <span v-if="course.course_head.organisme"
          class="bg-blue-900 text-white text-xs font-bold rounded px-3 py-1">{{ course.course_head.organisme }}</span>
        <span v-if="course.status"
          class="bg-primary text-white text-xs font-bold rounded px-3 py-1">{{ course.status.toUpperCase() }}</span>
      </div>
    </div>

    <!-- Meta & CTA -->
    <div class="flex flex-col md:flex-row gap-8 mb-6">
      <!-- Left info -->
      <div class="flex-1 space-y-2">
        <div class="flex items-center gap-2 text-gray-700">
          <i class="fa fa-map-marker-alt text-primary"></i>
          {{ course.course_head.nomlieu }}, {{ course.course_head.ville }}
        </div>
        <div class="flex items-center gap-2 text-gray-700">
          <i class="fa fa-calendar"></i>
          {{ formatDate(course.start_date) }}
          <span v-if="course.start_date !== course.end_date">
            - {{ formatDate(course.end_date) }}
          </span>
        </div>
        <div class="flex items-center gap-2 text-gray-700">
          <i class="fa fa-clock"></i>
          {{ formatTime(course.start_date) }} - {{ formatTime(course.end_date) }}
          <span class="ml-2 text-gray-400">
            ({{ getDuration(course.start_date, course.end_date) }} min)
          </span>
        </div>
        <div>
          <strong>Public :</strong>
          <span class="ml-2">Médecin généraliste et/ou spécialiste</span>
        </div>
        <div class="mt-6">
          <button
            v-if="!isRegistered"
            @click="register"
            class="bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-6 py-2 rounded amf-shadow text-lg transition"
          >JE SOUHAITE M'INSCRIRE</button>
          <div v-else class="flex flex-col gap-2">
            <span class="text-green-700 font-semibold">Vous êtes inscrit à cette formation</span>
            <!-- Add user-specific actions here: -->
            <div class="flex gap-2 mt-2">
              <button
                v-for="section in sectionList"
                :key="section.session_section_id"
                v-if="section.type === 'FORMULAIRE'"
                class="amf-bg-primary text-white font-semibold px-4 py-2 rounded"
                @click="$emit('open-quiz', section)"
              >
                Remplir le quiz
              </button>
              <a
                v-for="section in sectionList"
                :key="'zoom'+section.session_section_id"
                v-if="section.type === 'PRESENTATION' && section.zoom_url"
                :href="section.zoom_url"
                target="_blank"
                class="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold px-4 py-2 rounded"
              >
                Rejoindre Zoom
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Right: Cover/Experts -->
      <div class="flex-1 flex flex-col items-center gap-3">
        <img
          v-if="course.cover"
          :src="course.cover"
          class="max-w-sm rounded-xl shadow"
          alt="Course cover"
        />
        <!-- Placeholders if needed -->
        <img
          v-else
          src="/images/course_image.jpg"
          class="max-w-sm rounded-xl shadow"
          alt="Course cover"
        />
        <div class="mt-2 text-sm">
          <strong>Intervenants :</strong>
          <div>
            <span class="font-bold"><i class="fa fa-user-md mr-1"></i>Dr Nader BASSILIOS</span>
            <span class="block text-gray-600">Spécialiste en Néphrologie</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Description -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold text-accent mb-2">Description</h2>
      <div class="text-gray-900" style="white-space: pre-line">{{ course.description || '—' }}</div>
    </div>

    <!-- Sections/Steps -->
    <div class="mt-10">
      <h3 class="text-xl font-semibold text-accent mb-3">Les étapes de cette formation</h3>
      <div v-if="sectionList.length" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-7">
        <div
          v-for="section in sectionList"
          :key="section.session_section_id"
          class="bg-white rounded-2xl shadow px-4 py-5"
        >
          <div class="font-bold text-primary mb-1">{{ section.name }}</div>
          <div class="text-gray-800 text-sm">{{ section.description || section.obs || '—' }}</div>
          <div v-if="section.start_time" class="mt-2 text-xs text-gray-500">
            {{ formatDate(section.start_time) }}<br>
            {{ formatTime(section.start_time) }} - {{ formatTime(section.end_time) }}
          </div>
          <div v-if="section.type==='PRESENTATION' && section.zoom_url" class="mt-3">
            <a :href="section.zoom_url" target="_blank" class="inline-block px-3 py-1 rounded bg-indigo-600 text-white hover:bg-indigo-700">
              <i class="fa fa-video-camera"></i> Zoom
            </a>
          </div>
        </div>
      </div>
      <div v-else class="text-gray-400 text-center p-6">Aucune étape définie</div>
    </div>
  </div>
</template>

<style scoped>

</style>
