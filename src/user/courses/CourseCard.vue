<script setup lang="ts">
import { ref, computed } from "vue";
import CourseProgressBar from "./CourseProgressBar.vue";
import SectionTimeline from "./SectionTimeline.vue";
import { formatTimeHHMM, formatDateRange, isSameDayParis } from "../../useParisTime";
import type { Course, NextAction } from "./useCoursesLogic";
import { useCourseFiles } from "./useCourseFiles";
import { downloadDocument as fetchAssetBlob } from "../../services"; // alias to avoid name clash
import type { NormalizedFile } from "./useCourseFiles";
import VenueAddress from "../../site/VenueAddress.vue";
import AisyngChip from "../../modules/chips/AisyngChips.vue";

/* ---------- Props & Emits ---------- */
const props = defineProps<{
  course: Course & {
    files?: any[];
    _progress?: { completed: number; total: number; ratio: number };
    _next?: NextAction | null;
  };
  isOnlineVenue: (head: any) => boolean;
  onImageError: (e: Event) => void;
}>();

const emit = defineEmits<{
  (e: "open-quiz", payload: { section: any; course: Course }): void;
}>();

/* ---------- Files (from composable) ---------- */
const { filesList, formatBytes } = useCourseFiles({
  source: () => (props.course as any)?.files,
  sortByDocType: true,
});

/* ---------- Small UI helpers to keep template lean ---------- */
type FileKind = "pdf" | "doc" | "xls" | "ppt" | "image" | "zip" | "video" | "audio" | "txt" | "other";

const kindClasses = (k: FileKind) => {
  const bubble: Record<FileKind, string> = {
    pdf: "bg-red-50 text-red-700 ring-red-200",
    doc: "bg-blue-50 text-blue-700 ring-blue-200",
    xls: "bg-green-50 text-green-700 ring-green-200",
    ppt: "bg-orange-50 text-orange-700 ring-orange-200",
    image: "bg-fuchsia-50 text-fuchsia-700 ring-fuchsia-200",
    zip: "bg-amber-50 text-amber-700 ring-amber-200",
    video: "bg-purple-50 text-purple-700 ring-purple-200",
    audio: "bg-indigo-50 text-indigo-700 ring-indigo-200",
    txt: "bg-gray-50 text-gray-700 ring-gray-200",
    other: "bg-gray-50 text-gray-700 ring-gray-200",
  };
  const badge: Record<FileKind, string> = {
    pdf: "bg-red-200 text-red-900",
    doc: "bg-blue-200 text-blue-900",
    xls: "bg-green-200 text-green-900",
    ppt: "bg-orange-200 text-orange-900",
    image: "bg-fuchsia-200 text-fuchsia-900",
    zip: "bg-amber-200 text-amber-900",
    video: "bg-purple-200 text-purple-900",
    audio: "bg-indigo-200 text-indigo-900",
    txt: "bg-gray-200 text-gray-900",
    other: "bg-gray-200 text-gray-900",
  };
  return { bubble: bubble[k] ?? bubble.other, badge: badge[k] ?? badge.other };
};

const docTypeMeta = (t?: string | null) => {
  const v = (t || "").toLowerCase();
  if (v === "certificat")
    return { label: "Certificat", cls: "bg-emerald-100 text-emerald-800 ring-emerald-200" };
  if (v === "bibliographie")
    return { label: "Bibliographie", cls: "bg-sky-100 text-sky-800 ring-sky-200" };
  return null;
};

/* ---------- Downloader (kept tiny) ---------- */
const downloading = ref<Record<string, boolean>>({});

const keyOf = (f: any) => (f.id ?? f.session_asset_id ?? f.key).toString();

const triggerDownload = (url: string, name: string) => {
  const a = document.createElement("a");
  a.href = url;
  a.download = name || "document";
  a.rel = "noopener";
  document.body.appendChild(a);
  a.click();
  a.remove();
};

const isDownloading = (f: any) => !!downloading.value[keyOf(f)];

async function handleDownload(f: NormalizedFile & { id?: string | number; session_asset_id?: string | number; href?: string }) {
  const k = keyOf(f);
  try {
    downloading.value[k] = true;
    const assetId = f.session_asset_id ?? f.id;

    if (assetId != null) {
      // Use your service to fetch the blob
      const res = await fetchAssetBlob(assetId); // expected to return Blob or Response
      let blob: Blob;

      if (res instanceof Blob) {
        blob = res;
      } else if (res && typeof (res as any).blob === "function") {
        blob = await (res as Response).blob();
      } else {
        // last resort: treat as ArrayBuffer/Uint8Array
        const buf = (res as any) as ArrayBuffer;
        blob = buf ? new Blob([buf]) : new Blob();
      }

      const url = URL.createObjectURL(blob);
      triggerDownload(url, f.name);
      URL.revokeObjectURL(url);
    } else if (f.href) {
      // Fallback to direct href if no server id
      triggerDownload(f.href, f.name);
    } else {
      throw new Error("No id or href to download.");
    }
  } catch (e) {
    console.error("Download failed:", e);
    // optionally show a toast
  } finally {
    downloading.value[k] = false;
  }
}

/* ---------- Collapsible & date chip (unchanged) ---------- */
const sectionsOpen = ref(false);
const sectionsCount = computed(() => Array.isArray(props.course.sections) ? props.course.sections.length : 0);
const contentId = computed(() => `course-sections-${props.course.session_id}`);

const courseDateChip = computed(() => {
  const s = props.course.start_date;
  const e = props.course.end_date;
  if (!s && !e) return "";
  const fmt = new Intl.DateTimeFormat("fr-FR", {
    timeZone: "Europe/Paris", day: "2-digit", month: "short", year: "numeric",
  });
  if (s && e) {
    const sd = new Date(s); const ed = new Date(e);
    return isSameDayParis(sd, ed) ? fmt.format(sd) : `${fmt.format(sd)} — ${fmt.format(ed)}`;
  }
  if (s) return fmt.format(new Date(s));
  return `jusqu’au ${fmt.format(new Date(e!))}`;
});

/* ---------- Transition helpers (unchanged) ---------- */
const setTransition = (e: HTMLElement) => { e.style.transition = "height 280ms cubic-bezier(.2,.7,.2,1), opacity 200ms ease"; e.style.willChange = "height, opacity"; };
const clearTransition = (e: HTMLElement) => { e.style.transition = ""; e.style.willChange = ""; };
const onBeforeEnter = (el: Element) => { const e = el as HTMLElement; e.style.overflow = "hidden"; e.style.height = "0px"; e.style.opacity = "0"; };
const onEnter = (el: Element) => { const e = el as HTMLElement; void e.offsetHeight; setTransition(e); e.style.height = e.scrollHeight + "px"; e.style.opacity = "1"; };
const onAfterEnter = (el: Element) => { const e = el as HTMLElement; e.style.height = "auto"; e.style.overflow = ""; clearTransition(e); };
const onBeforeLeave = (el: Element) => { const e = el as HTMLElement; e.style.overflow = "hidden"; e.style.height = e.scrollHeight + "px"; e.style.opacity = "1"; void e.offsetHeight; };
const onLeave = (el: Element) => { const e = el as HTMLElement; setTransition(e); e.style.height = "0px"; e.style.opacity = "0"; };
const onAfterLeave = (el: Element) => { const e = el as HTMLElement; e.style.overflow = ""; clearTransition(e); };
</script>

<template>
  <div class="bg-white rounded-2xl shadow p-6 hover:shadow-lg transition">
    <!-- Header row -->
    <div class="flex flex-col md:flex-row gap-6">
      <!-- Image -->
      <div class="flex-shrink-0 w-full md:w-40 h-32">
        <img
          v-if="course.course_head?.image"
          :src="course.course_head.image"
          :alt="course.course_head.libellestage || course.title || 'Formation'"
          class="rounded-xl shadow-md w-full h-full object-cover"
          @error="onImageError"
        />
        <img
          v-else
          src="/images/course_image.jpg"
          :alt="course.course_head?.libellestage || course.title || 'Formation'"
          class="rounded-xl shadow-md w-full h-full object-cover"
        />
      </div>

      <!-- Main -->
      <div class="flex-1 min-w-0">
        <div class="flex flex-wrap items-center gap-3 mb-2">
          <h2 class="text-xl md:text-xl font-bold mb-6 text-center text-[var(--color-amf-primary)]">
            {{ course.course_head?.libellestage || course.title }}
          </h2>
        </div>
        <div class="flex flex-wrap items-center gap-3 mb-2">
          <AisyngChip
            :color="course.temporalStatus === 'done' ? 'green' : course.temporalStatus === 'in-progress' ? 'amber' : 'teal'"
          >
            {{
              course.temporalStatus === 'done' ? 'Terminé' : course.temporalStatus === 'in-progress' ? 'En cours' : 'À venir'
            }}
          </AisyngChip>
          <AisyngChip
            :color="isOnlineVenue(course.course_head) ? 'indigo' : 'blue'"
          >
            {{ isOnlineVenue(course.course_head) ? 'En ligne' : 'Présentiel' }}
          </AisyngChip>

          <!-- Course date interval chip -->
          <AisyngChip v-if="courseDateChip" color="teal" :title="courseDateChip">
            <span aria-hidden="true">📆</span>
            {{ courseDateChip }}
          </AisyngChip>
        </div>

        <p v-if="course.desc" class="text-gray-600 mb-3">
          {{ course.desc }}
        </p>

        <!-- Progress -->
        <CourseProgressBar
          v-if="course._progress && course._progress.total > 0"
          :completed="course._progress.completed"
          :total="course._progress.total"
          :percent="course._progress.ratio"
          class="mb-3"
        />


      </div>

      <!-- Right column slot (venue, etc.) -->
      <div v-if="!isOnlineVenue(course.course_head)" class="hidden md:block md:w-64">
        <VenueAddress
              v-if="!isOnlineVenue(course.course_head)"
              :name="course.course_head?.nomlieu || course.course_head?.location_name || course.course_head?.liblieu || null"
              :addr1="course.course_head?.adresse1?.trim()"
              :addr2="course.course_head?.adresse2?.trim()"
              :city="course.course_head?.ville?.trim()"
              :postalCode="course.course_head?.cp?.trim()"
              :region="course.course_head?.region?.trim()"
            />
      </div>
    </div>

    <!-- ACTION BAR -->
    <div class="mt-4 flex flex-wrap items-center gap-3">
      <button
        v-if="sectionsCount > 0"
        class="inline-flex items-center gap-2 text-sm font-medium px-3 py-2 rounded-md border border-gray-300 bg-white hover:bg-gray-50"
        :aria-expanded="sectionsOpen"
        :aria-controls="contentId"
        @click="sectionsOpen = !sectionsOpen"
      >
        <svg class="w-4 h-4 transition-transform" :class="sectionsOpen ? 'rotate-180' : ''" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.16l3.71-2.93a.75.75 0 111.04 1.08l-4.23 3.34a.75.75 0 01-.94 0L5.21 8.31a.75.75 0 01.02-1.1z" clip-rule="evenodd"/>
        </svg>
        <span>{{ sectionsOpen ? 'Masquer les étapes de la formation' : 'Voir les étapes de la formation' }}</span>
        <span class="text-gray-500">({{ sectionsCount }})</span>
      </button>
    </div>

    <!-- Collapsible: Sections timeline -->
    <div class="mt-0">
      <Transition
        @before-enter="onBeforeEnter"
        @enter="onEnter"
        @after-enter="onAfterEnter"
        @before-leave="onBeforeLeave"
        @leave="onLeave"
        @after-leave="onAfterLeave"
      >
        <div v-show="sectionsOpen" :id="contentId">
          <SectionTimeline
            :sections="course.sections || []"
            :course="course"
            @open-quiz="p => $emit('open-quiz', p)"
          />
        </div>
      </Transition>
    </div>

    <!-- Documents -->
    <div v-if="filesList.length" class="mt-6 border-t border-gray-200 pt-4">
      <h3 class="text-sm font-semibold text-gray-900 mb-3">Documents</h3>

      <ul class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
        <li v-for="f in filesList" :key="f.key">
          <button
            type="button"
            @click="handleDownload(f)"
            class="group flex items-center gap-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50 disabled:opacity-60 disabled:cursor-wait w-full text-left"
            :aria-label="`Télécharger ${f.name}`"
            :title="f.name"
            :disabled="isDownloading(f)"
            :aria-busy="isDownloading(f)"
          >
            <!-- Icon bubble -->
            <div class="relative w-10 h-10 flex items-center justify-center rounded-lg ring-1" :class="kindClasses(f.kind).bubble" aria-hidden="true">
              <svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.414a1.5 1.5 0 0 0-.44-1.06L12.646 2.44A1.5 1.5 0 0 0 11.586 2H4.5ZM11 3.5v3A1.5 1.5 0 0 0 12.5 8h3"/>
              </svg>
              <span class="absolute -bottom-1 right-1 text-[10px] font-medium px-1 rounded" :class="kindClasses(f.kind).badge">
                {{ (f.ext || f.kind).toUpperCase() }}
              </span>
            </div>

            <!-- Text -->
            <div class="min-w-0 flex-1">
              <div v-if="docTypeMeta(f.docType)" class="mb-0.5">
                <span class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full ring-1" :class="docTypeMeta(f.docType)!.cls">
                  {{ docTypeMeta(f.docType)!.label }}
                </span>
              </div>
              <p class="text-sm font-medium text-gray-900 truncate">{{ f.name }}</p>
              <p v-if="f.size" class="text-xs text-gray-500">{{ formatBytes(f.size) }}</p>
            </div>

            <!-- Right icon -->
            <svg v-if="!isDownloading(f)" class="w-5 h-5 text-gray-400 group-hover:text-gray-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path d="M10 3a.75.75 0 0 1 .75.75v7.19l2.22-2.22a.75.75 0 1 1 1.06 1.06l-3.5 3.5a.75.75 0 0 1-1.06 0l-3.5-3.5a.75.75 0 1 1 1.06-1.06l2.22 2.22V3.75A.75.75 0 0 1 10 3Z"/>
              <path d="M4 15.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H4.75a.75.75 0 0 1-.75-.75Z"/>
            </svg>
            <svg v-else class="w-5 h-5 animate-spin text-gray-400" viewBox="0 0 24 24" aria-hidden="true">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"/>
              <path d="M22 12a10 10 0 0 1-10 10" stroke="currentColor" stroke-width="4" fill="none"/>
            </svg>
          </button>
        </li>
      </ul>
    </div>
  </div>
</template>
