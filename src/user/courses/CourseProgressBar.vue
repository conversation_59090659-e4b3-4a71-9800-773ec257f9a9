<script setup lang="ts">
defineProps<{ completed: number; total: number; percent: number }>();
</script>

<template>
  <div>
    <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
      <span>Progression</span>
      <span>{{ completed }}/{{ total }} ({{ percent }}%)</span>
    </div>
    <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
      <div class="h-2 rounded-full bg-blue-600" :style="{ width: percent + '%' }"></div>
    </div>
  </div>
</template>
