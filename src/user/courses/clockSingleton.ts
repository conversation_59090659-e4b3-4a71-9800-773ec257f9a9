// clock-singleton.ts
import { ServerClock } from "../../modules/clock/clock";

let _clock: ServerClock | null = null;

export function getClock(): ServerClock {
  if (_clock) return _clock;
  _clock = new ServerClock({
    timezone: "Europe/Paris",
    resyncIntervalMs: 45 * 60 * 1000,
    fallback: { enabled: true }, // optional
  });
  // Safe to call multiple times; internal guard avoids duplicates
  _clock.syncOnce();
  _clock.startAutoSync();
  return _clock;
}

// For tests/SSR bootstraps/overrides
export function setClock(instance: ServerClock | null) {
  _clock = instance;
}
