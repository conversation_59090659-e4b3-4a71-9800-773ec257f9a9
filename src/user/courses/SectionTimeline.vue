<script setup lang="ts">
import SectionItem from "./SectionItem.vue";
import type { Course, Section } from "./useCoursesLogic";

defineProps<{ sections: Section[]; course: Course }>();
const emit = defineEmits<{ (e: "open-quiz", payload: { section: Section; course: Course }): void }>();
</script>

<template>
  <div v-if="sections && sections.length" class="mt-6">
    <div class="relative">
      <div class="absolute left-3 top-0 bottom-0 w-px bg-gray-200"></div>
      <div class="space-y-3">
        <div v-for="section in sections" :key="section.session_section_id" class="pl-8">
          <SectionItem :section="section" :course="course" :show-course-title="false" @open-quiz="p => emit('open-quiz', p)" />
        </div>
      </div>
    </div>
  </div>
</template>
