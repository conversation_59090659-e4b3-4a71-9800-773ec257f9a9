<script setup lang="ts">
const model = defineModel<"all" | "upcoming" | "in-progress" | "done">({ required: true });
</script>

<template>
  <div class="flex items-center justify-center gap-2">
    <button
      class="px-4 py-2 rounded-full text-sm font-medium border"
      :class="model === 'all' ? 'bg-white border-gray-300 shadow' : 'bg-gray-100 border-transparent'"
      @click="model = 'all'"
    >Toutes</button>

    <button
      class="px-4 py-2 rounded-full text-sm font-medium border"
      :class="model === 'upcoming' ? 'bg-white border-gray-300 shadow' : 'bg-gray-100 border-transparent'"
      @click="model = 'upcoming'"
    >À venir</button>

    <button
      class="px-4 py-2 rounded-full text-sm font-medium border"
      :class="model === 'in-progress' ? 'bg-white border-gray-300 shadow' : 'bg-gray-100 border-transparent'"
      @click="model = 'in-progress'"
    >En cours</button>

    <button
      class="px-4 py-2 rounded-full text-sm font-medium border"
      :class="model === 'done' ? 'bg-white border-gray-300 shadow' : 'bg-gray-100 border-transparent'"
      @click="model = 'done'"
    >Terminées</button>
  </div>
</template>
