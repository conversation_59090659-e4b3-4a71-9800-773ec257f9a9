<script setup>
import { computed, watch, ref, onMounted } from "vue";
import {downloadQuizImage, gradeQuizAnswerWithAI} from "../services.js";
import {calculateBlockScore} from "./useQuizLogic.js";
//import {gradeQuizAnswerWithAI} from "@/plugins/amf/amf_db_services.js";

const props = defineProps({
  sessionSectionId: [String, Number],
  block: Object,
  responses: Object,
  includeScoring: [<PERSON><PERSON><PERSON>, <PERSON>],
  isLegacy: [<PERSON><PERSON><PERSON>, Number]
});

const imagePreviewUrl = ref('');
const scoreInputsDisabled = ref(false);
const aiGradingLoading = ref({}); // Track per QA item if needed

onMounted(() => {
  // Disable manual scoring for modern multi_choice
  if (!props.isLegacy && props.block?.type === 'multi_choice') {
    scoreInputsDisabled.value = true;
  }
});

const iconForBlock = computed(() => {
  switch (props.block.type) {
    case 'value': return '📊';
    case 'multi_choice': return '✔️';
    case 'free_text': return '📝';
    case 'mix': return '🔀';
    case 'content': return '📘';
    default: return '📄';
  }
});

async function gradeWithAI(qaItemId, blockContent, qaItemContent, correctAnswer, userAnswer, qaItem) {
  aiGradingLoading.value[qaItemId] = true;
  try {
    const gradingResponse = await gradeQuizAnswerWithAI(
      props.sessionSectionId,
      blockContent + "\n" + qaItemContent,
      correctAnswer,
      userAnswer
    );
    if (!gradingResponse || gradingResponse === "error") throw new Error('AI grading error');

    const msg =
      "Raisonnement de l'IA :\n\n" +
      gradingResponse.reasoning + "\n\n" +
      "Note proposée : " + gradingResponse.score + " / 1\n\n" +
      "Accepter cette note ?";

    if (window.confirm(msg)) {
      props.responses[qaItemId].score = gradingResponse.score;
      props.responses[qaItemId].score_ai = gradingResponse.score;
      props.responses[qaItemId].score_author = 'AI';
    }
  } catch (error) {
    window.alert("Erreur lors de la correction automatique par l'IA.");
  } finally {
    aiGradingLoading.value[qaItemId] = false;
  }
}

// Main blockScore calculation:
const blockScore = computed(() => calculateBlockScore(props.block, props.responses, props.isLegacy));

const scoreAuthor = computed(() =>
  props.block?.score_author ? props.block.score_author : null
);

function getScoreTooltip(type) {
  if (props.isLegacy)
    return "Pour les formulaires anciens, seul le score maximal attribué à une sous-question du bloc est retenu comme score du bloc.";
  switch (type) {
    case 'multi_choice':
      return "Score automatique : (bonnes réponses cochées - mauvaises réponses cochées) / nombre de bonnes réponses attendues, limité entre 0 et 1.";
    case 'mix':
      return "La moyenne des scores individuels de chaque sous-question.";
    case 'text':
      return "La réponse est corrigée manuellement par un correcteur ou l’IA. Le score attribué est compris entre 0 et 1 selon la qualité de la réponse.";
    default:
      return "Le score est attribué selon les règles propres à chaque type de question.";
  }
}

async function downloadImage(url) {
  if (!url) {
    imagePreviewUrl.value = '';
    return;
  }
  try {
    const blob = await downloadQuizImage(url);
    if (!(blob instanceof Blob)) throw new Error("Response is not a Blob");
    imagePreviewUrl.value = URL.createObjectURL(blob);
  } catch (err) {
    console.error("Erreur de chargement de l’image", err);
    imagePreviewUrl.value = '';
  }
}

function clampScore(qaId) {
  const raw = props.responses?.[qaId]?.score;
  let val = typeof raw === 'number' ? raw : parseFloat(raw);
  if (isNaN(val)) return;
  val = Math.max(0, Math.min(1, val));
  // Keep one decimal place by convention
  props.responses[qaId].score = Math.round(val * 10) / 10;
}

watch(() => props.block.filename, async (newUrl) => {
  await downloadImage(newUrl);
}, { immediate: true });
</script>

<template>
  <div class="rounded-xl border border-gray-200 bg-white shadow-sm p-3 md:p-4 space-y-3 md:space-y-4">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center gap-2 md:gap-3">
      <div class="flex items-center gap-2">
        <span class="text-xl" aria-hidden="true">{{ iconForBlock }}</span>
        <h3 class="text-base font-semibold text-gray-900">
          {{ block.name || 'Bloc sans contenu' }}
        </h3>
      </div>

      <div v-if="includeScoring"
           class="mt-1 md:mt-0 md:ml-auto flex flex-col sm:flex-row items-start sm:items-center gap-1.5 sm:gap-3">
        <div class="flex items-center gap-1.5">
          <div class="inline-flex items-center rounded-md bg-teal-50 ring-1 ring-teal-100 px-2 py-1">
            <span class="text-xl font-bold text-teal-700 leading-none"
                  :title="getScoreTooltip(block.type)">{{ blockScore.toFixed(1) }}</span>
          </div>
          <span class="text-gray-400 text-xs" :title="getScoreTooltip(block.type)">ℹ️</span>
        </div>

        <div v-if="scoreAuthor"
             class="inline-flex items-center rounded px-2 py-0.5 text-xs"
             :class="scoreAuthor === 'AI'
                      ? 'text-teal-800 bg-teal-50 ring-1 ring-teal-200 font-semibold'
                      : 'text-gray-600 bg-gray-50 ring-1 ring-gray-200'">
          <span v-if="scoreAuthor === 'AI'" class="mr-0.5" aria-hidden="true">🤖</span>
          <span class="whitespace-nowrap">
            <span class="text-gray-500 mr-0.5">Corrigé par</span>{{ scoreAuthor }}
          </span>
        </div>
      </div>
    </div>

    <!-- Image -->
    <div v-if="imagePreviewUrl" class="rounded-lg border border-gray-200 overflow-hidden">
      <img :src="imagePreviewUrl"
           alt="Block image"
           class="w-full h-auto object-contain max-h-48 md:max-h-56" />
    </div>

    <!-- QA Items -->
    <div class="space-y-2 md:space-y-3">
      <div v-for="qaItem in block.qa" :key="qaItem.id"
           class="rounded-lg border border-gray-200 bg-white overflow-hidden">
        <!-- Content first, grading second (right on md+) -->
        <div class="grid grid-cols-1 md:[grid-template-columns:1fr_max-content]">
          <!-- Editable Answer column (LEFT on md+) -->
          <div class="p-3 flex flex-col gap-2 text-sm">
            <!-- Checkbox types -->
            <div v-if="qaItem.type === 'checkbox' || qaItem.type === 'maybe_checkbox'">
              <label class="inline-flex items-start gap-1.5">
                <input
                  type="checkbox"
                  class="mt-0.5 h-4 w-4 rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                  :checked="responses[qaItem.id].response_check === 1 || responses[qaItem.id].response_check === true"
                  @change="responses[qaItem.id].response_check = $event.target.checked ? 1 : 0"
                  aria-label="Cocher la réponse"
                />
                <span class="text-gray-800">{{ qaItem.content }}</span>
              </label>
            </div>

            <!-- Free text type -->
            <div v-else-if="qaItem.type === 'text'" class="space-y-1.5">
              <p class="text-gray-900 font-medium">{{ qaItem.content }}</p>
              <textarea
                v-model="responses[qaItem.id].response_text"
                rows="3"
                placeholder="Réponse…"
                class="w-full min-h-20 rounded border border-gray-300 px-2 py-1 text-sm text-gray-900 shadow-sm
                       focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-teal-500"
                aria-label="Réponse texte"
              ></textarea>
              <div v-if="qaItem.correct_text_answer" class="flex items-center gap-2">
                <button
                  class="inline-flex items-center gap-1 rounded border border-teal-300 px-2.5 py-1 text-xs
                         text-teal-700 hover:bg-teal-50 active:bg-teal-100 transition disabled:opacity-50"
                  :disabled="aiGradingLoading[qaItem.id]"
                  @click="gradeWithAI(qaItem.id, block.name, qaItem.content, qaItem.correct_text_answer, responses[qaItem.id].response_text)"
                  title="Utiliser l’IA pour corriger la réponse"
                >
                  🤖 Corriger par IA
                </button>
                <span v-if="aiGradingLoading[qaItem.id]" class="text-teal-700 text-xs animate-pulse">Analyse…</span>
              </div>
            </div>

            <!-- Value type -->
            <div v-else-if="qaItem.type === 'value'" class="space-y-1.5">
              <p class="text-gray-900 font-medium">{{ qaItem.content }}</p>
              <input
                type="number"
                class="w-32 rounded border border-gray-300 px-2 py-1 text-sm text-gray-900 shadow-sm
                       focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-teal-500"
                v-model.number="responses[qaItem.id].response_value"
                placeholder="Valeur…"
                aria-label="Réponse valeur"
              />
            </div>

            <!-- Fallback -->
            <div v-else>
              <p class="text-gray-800">{{ qaItem.content }}</p>
            </div>

            <!-- Scoring Details -->
            <div v-if="includeScoring" class="mt-1">
              <div class="rounded border border-gray-200 bg-gray-50 p-2 text-xs text-gray-700 space-y-0.5">
                <div v-if="qaItem.option_is_correct">✔ <span class="font-medium">Réponse correcte</span></div>
                <div v-if="qaItem.correct_text_answer">
                  <span class="font-medium">Texte attendu:</span> {{ qaItem.correct_text_answer }}
                </div>
                <div v-if="qaItem.correct_value_answer">
                  <span class="font-medium">Valeur attendue:</span> {{ qaItem.correct_value_answer }}
                </div>
              </div>
            </div>
          </div>

          <!-- Grading column (RIGHT on md+) -->
          <div v-if="includeScoring && !scoreInputsDisabled"
               class="bg-teal-50/30 md:bg-teal-50/20 p-3 border-t md:border-t-0 md:border-l border-teal-100">
            <div class="flex flex-col gap-2">
              <!-- Label -->
              <label class="text-xs font-medium text-gray-700 max-w-[6rem]">Score</label>

              <!-- Numeric input (full width, compact) -->
              <input
                class="max-w-[6rem] w-[6rem] rounded border border-gray-300 px-2 py-1 text-sm text-gray-900 shadow-sm
                       focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-teal-500
                       disabled:bg-gray-100 disabled:text-gray-400"
                type="number"
                v-model.number="responses[qaItem.id].score"
                min="0" max="1" step="0.01"
                :disabled="scoreInputsDisabled"
                @blur="clampScore(qaItem.id)"
                placeholder="0–1"
                aria-label="Score numérique"
              />

              <!-- Quick buttons row -->
              <div class="flex flex-row items-center gap-1">
                <button
                  @click="responses[qaItem.id].score = 0"
                  class="rounded border px-2 py-0.5 text-xs
                         border-teal-200 text-teal-700 hover:bg-teal-50 active:bg-teal-100 transition
                         disabled:opacity-50"
                  :disabled="scoreInputsDisabled">0</button>
                <button
                  @click="responses[qaItem.id].score = 0.5"
                  class="rounded border px-2 py-0.5 text-xs
                         border-teal-200 text-teal-700 hover:bg-teal-50 active:bg-teal-100 transition
                         disabled:opacity-50"
                  :disabled="scoreInputsDisabled">0.5</button>
                <button
                  @click="responses[qaItem.id].score = 1"
                  class="rounded border px-2 py-0.5 text-xs
                         border-teal-200 text-teal-700 hover:bg-teal-50 active:bg-teal-100 transition
                         disabled:opacity-50"
                  :disabled="scoreInputsDisabled">1</button>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>


