<script setup>
import { ref, watch, computed } from 'vue'
import {
  TextBoxComponent as EjsTextbox,
} from '@syncfusion/ej2-vue-inputs'
import { DropDownListComponent as EjsDropdownlist } from '@syncfusion/ej2-vue-dropdowns'
import { ButtonComponent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@syncfusion/ej2-vue-buttons'
import { ToastUtility } from '@syncfusion/ej2-notifications'

import GenericFieldSelector from '@/components/GenericFieldSelector.vue'
import { saveUtilisateur } from '@/plugins/amf/amf_db_services.js'
import { useLocalSpinner } from '@/composables/useLocalSpinner.js'
import LocalSpinnerOverlay from '@/plugins/amf/components/LocalSpinnerOverlay.vue'

const emit = defineEmits(['saved'])

const props = defineProps({
  // Pass the current user object (utilisateur from your domain model)
  utilisateur: { type: Object, required: true },
  showHeader: { type: Boolean, default: true },
  // If username is managed elsewhere, set this to true to make it readonly in UI
  usernameReadonly: { type: Boolean, default: false }
})

// local copy so we don't mutate props directly
const clone = (obj) => JSON.parse(JSON.stringify(obj ?? {}))
const u = ref(clone(props.utilisateur))

watch(() => props.utilisateur, (nv) => { u.value = clone(nv) }, { deep: true })

const { spinnerVisible, spinnerMessage, showSpinner, hideSpinner } = useLocalSpinner()

const preferredAddressOptions = [
  'Adresse Professionnelle',
  'Adresse Personnelle',
  'Ne souhaite pas être contacté(e)',
  'Adresse introuvable'
]

const isValid = computed(() => {
  const v = u.value || {}
  return !!(v.nom && v.prenom && v.email && v.telmob)
})

async function handleSave () {
  showSpinner('Enregistrement…')
  try {
    const payload = clone(u.value)
    const id = await saveUtilisateur(payload, null)
    if (!id || id === 'error') throw new Error('Save failed')

    // ensure local model carries the id after first save
    u.value.id = u.value.id || id

    ToastUtility.show('Compte mis à jour avec succès', 'Success', 3000)
    emit('saved', clone(u.value))
  } catch (e) {
    console.error(e)
    ToastUtility.show("Échec de l’enregistrement. Réessayez.", 'Error', 4000)
  } finally {
    hideSpinner()
  }
}
</script>

<template>
  <div class="moncompte-wrapper">
    <LocalSpinnerOverlay :visible="spinnerVisible" :message="spinnerMessage" />

    <div v-if="showHeader" class="moncompte-header">
      <div class="header-title">👤 Mon compte</div>
      <div class="header-sub">Mettez à jour vos informations essentielles</div>
    </div>

    <form class="form-card" @submit.prevent="handleSave">
      <!-- Identity & Contact -->
      <fieldset class="section">
        <legend>Identité & contact</legend>

        <div class="grid">
          <div :class="['field', !u.nom ? 'aisyng-required-missing' : '']">
            <label for="civilite">Civilité</label>
            <GenericFieldSelector
              id="civilite"
              v-model="u.civilite"
              fieldName="civilite"
              datatypeName="utilisateur"
              datasourceName="main"
            />
          </div>

          <div :class="['field', !u.prenom ? 'aisyng-required-missing' : '']">
            <label for="prenom">Prénom</label>
            <ejs-textbox id="prenom" v-model="u.prenom" required="" />
          </div>

          <div :class="['field', !u.nom ? 'aisyng-required-missing' : '']">
            <label for="nom">Nom</label>
            <ejs-textbox id="nom" v-model="u.nom" required="" />
          </div>

          <div :class="['field', !u.email ? 'aisyng-required-missing' : '']">
            <label for="email">Email</label>
            <ejs-textbox id="email" v-model="u.email" type="email" required="" />
          </div>

          <div :class="['field', !u.telmob ? 'aisyng-required-missing' : '']">
            <label for="telmob">Téléphone mobile</label>
            <ejs-textbox id="telmob" v-model="u.telmob" required="" />
          </div>

          <div class="field">
            <label for="username">Username</label>
            <ejs-textbox
              id="username"
              v-model="u.username"
              :readonly="usernameReadonly"
              autocomplete="username"
            />
          </div>
        </div>
      </fieldset>

      <!-- Professional (light) -->
      <fieldset class="section">
        <legend>Informations professionnelles (facultatif)</legend>

        <div class="grid">
          <div class="field">
            <label for="profession">Profession</label>
            <GenericFieldSelector
              id="profession"
              v-model="u.profession"
              fieldName="profession"
              datatypeName="utilisateur"
              datasourceName="main"
            />
          </div>

          <div class="field">
            <label for="specialite">Spécialité</label>
            <GenericFieldSelector
              id="specialite"
              v-model="u.specialite"
              fieldName="specialite"
              datatypeName="utilisateur"
              datasourceName="main"
            />
          </div>

          <div class="field">
            <label for="telprof">Téléphone professionnel</label>
            <ejs-textbox id="telprof" v-model="u.telprof" />
          </div>

          <div class="field">
            <label for="numrpps">Numéro RPPS</label>
            <ejs-textbox id="numrpps" v-model="u.numrpps" />
          </div>

          <div class="field">
            <label for="numadeli">Numéro ADELI</label>
            <ejs-textbox id="numadeli" v-model="u.numadeli" />
          </div>
        </div>
      </fieldset>

      <!-- Contact preference -->
      <fieldset class="section">
        <legend>Préférences de contact</legend>

        <div class="grid">
          <div class="field">
            <label for="adresse_autiliser">Adresse à utiliser</label>
            <ejs-dropdownlist
              id="adresse_autiliser"
              v-model="u.adresse_autiliser"
              :dataSource="preferredAddressOptions"
            />
          </div>
        </div>
      </fieldset>

      <!-- Addresses -->
      <fieldset class="section">
        <legend>Adresse professionnelle</legend>

        <div class="grid">
          <div class="field"><label for="adresseprof1">Adresse 1</label><ejs-textbox id="adresseprof1" v-model="u.adresseprof1" /></div>
          <div class="field"><label for="adresseprof2">Adresse 2</label><ejs-textbox id="adresseprof2" v-model="u.adresseprof2" /></div>
          <div class="field"><label for="cpprof">Code postal</label><ejs-textbox id="cpprof" v-model="u.cpprof" /></div>
          <div class="field"><label for="villeprof">Ville</label><ejs-textbox id="villeprof" v-model="u.villeprof" /></div>
          <div class="field">
            <label for="region">Région</label>
            <GenericFieldSelector
              id="region"
              v-model="u.region"
              fieldName="region"
              datatypeName="utilisateur"
              datasourceName="main"
            />
          </div>
        </div>
      </fieldset>

      <fieldset class="section">
        <legend>Adresse personnelle</legend>

        <div class="grid">
          <div class="field"><label for="adressepers1">Adresse 1</label><ejs-textbox id="adressepers1" v-model="u.adressepers1" /></div>
          <div class="field"><label for="adressepers2">Adresse 2</label><ejs-textbox id="adressepers2" v-model="u.adressepers2" /></div>
          <div class="field"><label for="cppers">Code postal</label><ejs-textbox id="cppers" v-model="u.cppers" /></div>
          <div class="field"><label for="villepers">Ville</label><ejs-textbox id="villepers" v-model="u.villepers" /></div>
        </div>
      </fieldset>

      <div class="actions">
        <ejs-button cssClass="e-primary" :disabled="!isValid" @click="handleSave">
          💾 Enregistrer
        </ejs-button>
      </div>
    </form>
  </div>
</template>

<style scoped>
.moncompte-wrapper {
  width: 80vw;
  max-width: 980px;
  margin: 0 auto;
  padding: 16px;
}

.moncompte-header {
  background: #f9fafb;
  border: 1px solid #e0e6ed;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
}

.header-title {
  font-size: 1.3em;
  font-weight: 700;
  color: #2c3e50;
}
.header-sub {
  font-size: 0.95em;
  color: #7f8c8d;
}

.form-card {
  background: #fff;
  border: 1px solid #e0e6ed;
  border-radius: 12px;
  padding: 16px;
}

.section {
  border: none;
  margin: 0 0 18px 0;
  padding: 0;
}

.section > legend {
  font-weight: 600;
  margin-bottom: 8px;
  color: #2c3e50;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 14px;
}

.field {
  display: flex;
  flex-direction: column;
}
.field label {
  margin-bottom: 4px;
  font-weight: 500;
}

.actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 8px;
}

.aisyng-required-missing :deep(input) {
  border-color: #ef4444 !important;
}
</style>
