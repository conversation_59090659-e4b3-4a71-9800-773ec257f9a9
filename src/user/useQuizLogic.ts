


// Scoring method for multi_choice (non-legacy)
function autoMultiChoiceScore(block, responses) {
  if (!block.qa || !responses) return 0;
  const correctOptions = block.qa.filter(q => q.option_is_correct);
  const totalCorrect = correctOptions.length;
  if (totalCorrect === 0) return 0;
  let positive = 0;
  let negative = 0;
  for (const qaItem of block.qa) {
    const checked = (responses?.[qaItem.id]?.response_check === 1 || responses?.[qaItem.id]?.response_check === true);
    if (qaItem.option_is_correct && checked) positive += 1;
    if (!qaItem.option_is_correct && checked) negative += 1;
  }
  const raw = (positive - negative) / totalCorrect;
  return Math.max(0, Math.min(1, raw));
}

// MIX block scoring (modern, non-legacy)
function autoMixScore(block, responses) {
  if (!block.qa || !responses) return 0;
  const scores = [];
  for (const qaItem of block.qa) {
    if (['choice', 'multi_choice', 'checkbox', 'maybe_checkbox'].includes(qaItem.type)) {
      const checked = (responses?.[qaItem.id]?.response_check === 1 || responses?.[qaItem.id]?.response_check === true);
      const value =
        Math.max(0,
          Math.min(1,
            ( (qaItem.option_is_correct && checked ? 1 : 0) - (!qaItem.option_is_correct && checked ? 1 : 0) )
          )
        );
      scores.push(value);
    } else if (qaItem.type === 'text') {
      const s = responses?.[qaItem.id]?.score;
      const score = typeof s === 'number' ? s : parseFloat(s);
      if (!isNaN(score)) scores.push(Math.max(0, Math.min(1, score)));
    }
  }
  if (!scores.length) return 0;
  return scores.reduce((a, b) => a + b, 0) / scores.length;
}

// Main blockScore calculation:
export function calculateBlockScore(block, responses, isLegacy) {
  if (!block?.qa?.length) return 0;
  if (isLegacy) {
    // LEGACY: highest score among sub-questions
    return Math.max(...block.qa.map(qaItem => {
      const s = responses?.[qaItem.id]?.score;
      const val = typeof s === 'number' ? s : parseFloat(s);
      return isNaN(val) ? 0 : val;
    }), 0);
  }
  if (block.type === 'multi_choice') {
    // Modern multi_choice: automatic score
    return autoMultiChoiceScore(block, responses);
  }
  if (block.type === 'mix') {
    return autoMixScore(block, responses);
  }
  // DEFAULT: average
  const scores = block.qa.map(qaItem => {
    const s = responses?.[qaItem.id]?.score;
    const val = typeof s === 'number' ? s : parseFloat(s);
    return isNaN(val) ? 0 : val;
  });
  if (!scores.length) return 0;
  return scores.reduce((a, b) => a + b, 0) / scores.length;
}


// Minimal shapes; adapt to your actual types.
type QAItem = { id: string };
type Block = {
  id: string;
  type: string;
  qa: QAItem[];
  isLegacy?: boolean; // if you have it
};

type ResponsesByBlockId = Record<string, any>;

/**
 * Overall quiz score.
 * - If maxScore === 0 (default): returns the raw sum of block scores (null/NaN -> 0).
 * - If maxScore > 0: returns the normalised score in [0, maxScore], i.e.
 *     (average of per-block scores, after capping each to [0,1])) * maxScore
 */
export function calculateQuizScore(
  blocks: Block[],
  responsesByBlockId: ResponsesByBlockId,
  maxScore = 0
): number {
  if (!Array.isArray(blocks) || blocks.length === 0) return 0;

  let rawSum = 0;
  let cappedSum = 0;
  let count = 0;

  for (const block of blocks) {
    if (!block || !Array.isArray(block.qa) || block.qa.length === 0) continue;

    const perBlockResponses = responsesByBlockId?.[block.id];
    const blockScore = Number(
      calculateBlockScore(block as any, perBlockResponses, !!block.isLegacy)
    );

    const safeScore = Number.isFinite(blockScore) ? blockScore : 0;
    rawSum += safeScore;

    // For normalisation, cap to [0,1] because a block conceptually contributes at most 1.
    const capped = Math.max(0, Math.min(1, safeScore));
    cappedSum += capped;

    count += 1;
  }

  if (count === 0) return 0;

  if (maxScore > 0) {
    const average = cappedSum / count;        // in [0,1]
    return average * maxScore;                // in [0, maxScore]
  }

  // Raw total (can be in [0, count] for modern scoring; legacy may differ)
  return rawSum;
}
