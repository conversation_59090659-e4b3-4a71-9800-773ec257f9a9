// useParisTime.ts
import { ServerClock, parseAsParis } from "./modules/clock/clock";

// Minimal Any type helper
export type AnyObj = Record<string, any>;

// Today in Paris (midnight Paris) based on trusted server time
export function getTodayInParis(clock: ServerClock): Date {
  const now = clock.now(); // authoritative "now"
  const parts = new Intl.DateTimeFormat("fr-FR", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  }).formatToParts(now);
  const o = Object.fromEntries(parts.map(p => [p.type, p.value]));
  // Build midnight Paris wall time
  return parseAsParis(`${o.year}-${o.month}-${o.day}T00:00:00`)!;
}

export function isSameDayParis(date1?: Date | null, date2?: Date | null): boolean {
  if (!date1 || !date2) return false;
  const fmt = new Intl.DateTimeFormat("fr-FR", {
     year: "numeric", month: "2-digit", day: "2-digit"
  });
  return fmt.format(date1) === fmt.format(date2);
}

export function isTodayParis(clock: ServerClock, dateLike?: string | Date | null): boolean {
  if (!dateLike) return false;
  const d = dateLike instanceof Date ? dateLike : parseAsParis(dateLike);
  return !!d && isSameDayParis(d, getTodayInParis(clock));
}

export function formatDateShort(dateLike?: string | null): string {
  if (!dateLike) return "";
  const d = parseAsParis(dateLike)!;
  return new Intl.DateTimeFormat("fr-FR", {
    weekday: "short", day: "2-digit", month: "short"
  }).format(d);
}

export function formatTimeHHMM(dateLike?: string | null): string {
  if (!dateLike) return "";
  const d = parseAsParis(dateLike)!;
  return new Intl.DateTimeFormat("fr-FR", {
    hour: "2-digit", minute: "2-digit"
  }).format(d);
}

export function formatDateRange(start?: string | null, end?: string | null): string {
  if (!start && !end) return "";
  const s = start ? parseAsParis(start)! : null;
  const e = end ? parseAsParis(end)! : null;
  if (s && e && isSameDayParis(s, e)) {
    return `${formatDateShort(start!)} • ${formatTimeHHMM(start!)}–${formatTimeHHMM(end!)}`;
  }
  const left = s ? `${formatDateShort(start!)} ${formatTimeHHMM(start!)}` : "";
  const right = e ? `${formatDateShort(end!)} ${formatTimeHHMM(end!)}` : "";
  return `${left} → ${right}`.trim();
}



export function onImageError(e: Event) {
  const t = e.target as HTMLImageElement;
  t.src = "/images/course_image.jpg";
}


