<script setup lang="ts">
</script>

<template>
  <footer>
    <div class="footer-flex">
      <div class="footer-col footer-logo">
        <img src="/images/logo.png" alt="AMF Logo">
      </div>
      <div class="footer-col footer-links">
        <strong>Navigation</strong>
        <router-link :to="{ path: '/formations' }" >Nos Formations</router-link>
        <router-link :to="{ path: '/', hash: '#team' }" >Équipe</router-link>
        <router-link :to="{ path: '/', hash: '#contact' }" >Contact</router-link>
      </div>
      <div class="footer-col footer-links">
        <strong>Informations</strong>
        <a href="/mentionslegales">Mentions légales</a>
        <a href="/politiquesdeconfidentialite">Politique de confidentialité</a>
        <a href="/cgv">CGV</a>
      </div>
      <div class="footer-col">
        <a href="/documents/Qualiopi/10311783-AMF-FORMATION-Qualiopiv1.pdf" target="_blank">
          <img src="/images/associations/Qualiopi.png" alt="Label Qualiopi" style="height:150px; margin-top:0.8rem;">
        </a>
      </div>
    </div>
    <div class="footer-copy">
      © 2025 AMF Formation & Évaluation – 15 rue Guyton de Morveau, 75013 Paris ·
      <a href="mailto:<EMAIL>" style="color:#fff;text-decoration:underline;"><EMAIL></a>
    </div>
  </footer>
</template>
<style scoped>
footer {
  background: var(--amf-accent, #003049);
  color: #fff;
  padding: 2.3rem 1rem 1.3rem 1rem;
  font-size: 0.97rem;
  margin-top: 2.2rem;
}
.footer-flex {
  max-width: 1180px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
  align-items: flex-start;
}
.footer-col {
  min-width: 180px;
  margin-bottom: 1.2rem;
}
.footer-logo img {
  margin-top: 2rem;
  height: 60px;
  margin-bottom: 1.1rem;
}
.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}
.footer-links strong {
  font-size: 1.04rem;
  margin-bottom: 0.5rem;
  color: #e0f4fb;
}
.footer-links a {
  color: #fff;
  opacity: 0.86;
  margin-bottom: 0.2rem;
  text-decoration: none;
  transition: opacity 0.15s, text-decoration 0.15s;
}
.footer-links a:hover {
  opacity: 1;
  text-decoration: underline;
}
.footer-social {
  margin-top: 0.8rem;
  display: flex;
  gap: 1rem;
}
.footer-social a {
  color: #fff;
  font-size: 1.21rem;
  background: var(--amf-primary, #008891);
  padding: 0.33rem 0.7rem;
  border-radius: 8px;
  display: inline-block;
  transition: background 0.14s;
}
.footer-social a:hover {
  background: var(--amf-primary-dark, #005f73);
}
.footer-copy {
  text-align: center;
  opacity: 0.77;
  margin-top: 1.2rem;
  font-size: 0.95rem;
}
.footer-copy a {
  color: #fff;
  text-decoration: underline;
}
@media (max-width: 900px) {
  .footer-flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.7rem;
  }
}
@media (max-width: 600px) {
  footer {
    padding: 1.2rem 0.3rem 0.8rem 0.3rem;
  }
  .footer-flex {
    gap: 0.3rem;
  }
}
</style>
