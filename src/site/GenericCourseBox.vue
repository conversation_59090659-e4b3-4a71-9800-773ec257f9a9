<script setup>
import { computed } from 'vue'
import { imageUrl } from './useCourseBox.js'
import CourseBoxChips from './CourseBoxChips.vue'

const props = defineProps({
  course: { type: Object, required: true }
})

// Title with sensible fallback
const title = computed(() =>
  props.course?.libellestage?.trim() ||
  props.course?.name?.trim() ||
  'Formation'
)

// Support multiple possible field names and value shapes (0/1, boolean, "1")
const isRegistered = computed(() => {
  const v = props.course?.registered ?? props.course?.is_registered ?? props.course?.registered_flag
  return v === 1 || v === true || v === '1'
})

const titleId = computed(() => `course-title-${props.course?.session_id ?? 'unknown'}`)

function onImgError(e) {
  const img = e?.target
  if (img && img.tagName === 'IMG') {
    img.src = '/images/course_image.jpg'
  }
}
</script>

<template>
  <router-link
    :to="`/formations/${course.session_id}`"
    class="block rounded-2xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--color-amf-primary)] focus-visible:ring-offset-2 focus-visible:ring-offset-white"
    :aria-label="`Voir la formation : ${title}`"
  >
    <article
      class="relative rounded-2xl shadow-md border border-[var(--color-amf-primary-light)] bg-white/85 flex flex-col overflow-hidden hover:shadow-xl transition"
      :aria-labelledby="titleId"
    >
      <!-- Enrolled ribbon -->
      <div
        v-if="isRegistered"
        class="absolute right-3 top-3 z-10 flex items-center gap-1 rounded-full bg-[var(--color-amf-primary)]/95 px-2.5 py-1 text-xs font-semibold text-white shadow"
        aria-label="Vous êtes inscrit(e) à cette formation"
      >
        <!-- check icon -->
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 shrink-0" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-7.25 7.25a1 1 0 01-1.414 0l-3-3a1 1 0 111.414-1.414l2.293 2.293 6.543-6.543a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
        <span>Inscrit</span>
      </div>

      <!-- Image -->
      <div class="relative bg-[var(--color-amf-primary-light)]">
        <img
          :src="imageUrl(course)"
          :alt="`Visuel de la formation : ${title}`"
          class="w-full aspect-[16/9] object-cover"
          loading="lazy"
          decoding="async"
          fetchpriority="low"
          @error="onImgError"
        />
      </div>

      <!-- Title -->
      <h3
        :id="titleId"
        class="px-4 font-bold text-lg text-[var(--color-amf-primary)] leading-snug line-clamp-3
               flex items-center justify-center text-center min-h-[7rem]"
        :title="title"
      >
        {{ title }}
      </h3>

      <!-- Details chips -->
      <div class="px-4 pb-3">
        <CourseBoxChips :course="course" />
      </div>
    </article>
  </router-link>
</template>
