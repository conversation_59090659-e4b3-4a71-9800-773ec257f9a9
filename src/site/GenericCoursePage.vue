<script setup>
// Dummy data or props for course
import SiteFooter from "./SiteFooter.vue";
import SiteNavBar from "./SiteNavBar.vue";
import {useRoute, useRouter} from "vue-router";
import {getUpcomingCourses, registerToCourse, cancelCourseRegistration} from "../services.js";
import {loadStageImage} from "../formation.js";
import {computed, onMounted, ref} from "vue";
import {imageUrl} from "./useCourseBox.js";
import {useCurrentUser} from "vuefire";
import { useToast } from '../modules/toast/toast.js';

const toast = useToast()
const route = useRoute()
const router = useRouter()
const courseId = route.params.id
const course = ref(null)
const loading = ref(false)

const user = useCurrentUser();
const isAuthenticated = computed(() => !!user.value);

// NEW: local UI state
const showRegisterForm = ref(false)
const showCancelForm = ref(false)

// ---- NEW REGISTRATION STATE ----
// course.value.registration ∈ 'none' | 'participant' | 'wait' | 'invite' | 'cancel'
const registration = computed(() => course.value?.registration ?? 'none')

const regStatus = computed(() => {
  if (!isAuthenticated.value) return null
  switch (registration.value) {
    case 'participant':
      return { text: "Vous êtes inscrit(e) à cette formation.", tone: 'success' }
    case 'wait':
      return { text: "Vous êtes sur liste d’attente pour cette formation.", tone: 'info' }
    case 'invite':
      return { text: "Vous avez été invité(e) à cette formation.", tone: 'warning' }
    case 'cancel':
      return { text: "Vous avez annulé votre inscription.", tone: 'neutral' }
    default:
      return null
  }
})

const regStatusClass = computed(() => {
  const tone = regStatus.value?.tone
  switch (tone) {
    case 'success': return 'bg-green-50 border-green-200 text-green-900'
    case 'info':    return 'bg-blue-50 border-blue-200 text-blue-900'
    case 'warning': return 'bg-amber-50 border-amber-200 text-amber-900'
    case 'neutral': return 'bg-gray-50 border-gray-200 text-gray-900'
    default:        return ''
  }
})

// When to allow actions
const canRegister = computed(() => {
  // If not authenticated, show the same CTA as before (login first)
  if (!isAuthenticated.value) return true
  // If authenticated, registration is possible only if state is 'none'
  return registration.value === 'none'
})

const canCancel = computed(() => {
  // Cancellation is possible if logged in and not already cancelled
  return isAuthenticated.value && !['cancel', 'none'].includes(registration.value)
})

function toggleRegister() {
  showRegisterForm.value = !showRegisterForm.value
  if (showRegisterForm.value) showCancelForm.value = false
}

function toggleCancel() {
  showCancelForm.value = !showCancelForm.value
  if (showCancelForm.value) showRegisterForm.value = false
}

// Contact link used in error toasts
const contactHref = "/#contact"

// Handlers
async function confirmRegister() {
  try {
    await registerToCourse(courseId)
    toast.success('Nous avons bien reçu votre demande d’inscription à ce cours. Notre équipe vous contactera prochainement pour en discuter les détails. Nous vous remercions.', {
      title: 'Inscription réussie',
      position: 'center'
    })
    showRegisterForm.value = false
    await loadCourse() // refresh to get the updated registration state
  } catch (e) {
    console.error(e)
    toast.error('Nous n’avons pas pu finaliser votre inscription. Nous vous prions de bien vouloir contacter l’AMF via le formulaire de contact.', {
      title: 'Échec de l’inscription',
      position: 'bottom-center',
      action: {label: 'Contact AMF', href: contactHref},
      duration: 0, // sticky until dismissed
    })
  }
}

async function confirmCancel() {
  try {
    await cancelCourseRegistration(courseId)
    toast.info('Votre inscription a été annulée.', {
      title: 'Annulation réussie',
      position: 'bottom-center',
    })
    showCancelForm.value = false
    await loadCourse()
  } catch (e) {
    console.error(e)
    toast.error('Nous n’avons pas pu annuler votre inscription. Nous vous prions de bien vouloir nous contacter via le formulaire de contact.', {
      title: 'Échec de l’annulation',
      position: 'bottom-center',
      action: {label: 'Contact AMF', href: "/#contact"},
      duration: 0,
    })
  }
}

async function loadCourse() {
  loading.value = true
  try {
    const courses = await getUpcomingCourses(courseId, true)
    if (courses.length !== 1) {
      throw new Error("No course with id " + courseId + " found")
    }
    course.value = courses[0]
    // Ensure we have a normalized registration value
    if (!course.value.registration) course.value.registration = 'none'
    course.value.image = await loadStageImage(course.value.image_url)
  } catch (e) {
    console.error("Failed to load course:", e);
    course.value = null
  } finally {
    loading.value = false
  }
}

const steps = computed(() => {
  const stageData = course.value?.stage_data
  if (stageData) {
    return [stageData.step1, stageData.step2, stageData.step3]
  }
  return []
})

const obs = computed(() => course.value?.stage_data?.obs)
const experts = computed(() => course.value?.experts)

// ---- DATE/TIME HELPERS (existing) ----
function formatDate(dt) {
  if (!dt) return ''
  const d = new Date(dt)
  return d.toLocaleDateString('fr-FR', {
    weekday: 'long', day: '2-digit', month: 'long', year: 'numeric'
  })
}

function formatTime(start, end) {
  const s = new Date(start), e = new Date(end)
  return `${s.toLocaleTimeString('fr-FR', {
    hour: '2-digit',
    minute: '2-digit'
  })} – ${e.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})} (${Math.round((e - s) / 60000)} minutes)`
}

const isSameDay = computed(() => {
  if (!course.value) return true
  const s = new Date(course.value.start_time)
  const e = new Date(course.value.end_time)
  return s.getFullYear() === e.getFullYear()
      && s.getMonth() === e.getMonth()
      && s.getDate() === e.getDate()
})

function formatDateTimeRange(start, end) {
  const s = new Date(start)
  const e = new Date(end)
  const sDate = s.toLocaleDateString('fr-FR', {weekday: 'long', day: '2-digit', month: 'long', year: 'numeric'})
  const sTime = s.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})
  const eDate = e.toLocaleDateString('fr-FR', {weekday: 'long', day: '2-digit', month: 'long', year: 'numeric'})
  const eTime = e.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})
  return `Du ${sDate} ${sTime} au ${eDate} ${eTime}`
}

onMounted(async () => {
  await loadCourse();
})
</script>

<template>
  <div class="min-h-screen bg-white flex flex-col">
    <!-- NAVBAR -->
    <SiteNavBar/>

    <!-- Main Content -->
    <main v-if="course" class="flex-1 max-w-6xl mx-auto px-2 sm:px-6 py-8">
      <!-- Title & Info -->
      <div class="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6">
        <!-- Main info (left) -->
        <div class="flex-1">
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-1 leading-snug">
            {{ course.libellestage || course.name }}
          </h1>

          <div class="flex flex-col sm:flex-row sm:items-center gap-2 text-gray-700 text-base mb-2">
            <div>
              <i class="fa fa-university text-[var(--color-amf-primary)] mr-1"></i>
              {{ course.organisme || 'AMF' }}
              <span v-if="course.is_online" class="ml-3">
                <i class="fa fa-globe text-[var(--color-amf-primary)]"></i>
                En ligne
              </span>
              <span v-else class="ml-3">
                <i class="fa fa-location-dot text-[var(--color-amf-primary)]"></i>
                {{ course.location_city || course.location_name }}
              </span>
            </div>

            <!-- DATE/TIME -->
            <template v-if="isSameDay">
              <div>
                <i class="fa-regular fa-calendar text-[var(--color-amf-primary)] mr-1"></i>
                {{ formatDate(course.start_time) }}
              </div>
              <div>
                <i class="fa-regular fa-clock text-[var(--color-amf-primary)] mr-1"></i>
                {{ formatTime(course.start_time, course.end_time) }}
              </div>
            </template>
            <template v-else>
              <div>
                <i class="fa-regular fa-calendar text-[var(--color-amf-primary)] mr-1"></i>
                {{ formatDateTimeRange(course.start_time, course.end_time) }}
              </div>
            </template>
          </div>

          <!-- Publics concernes -->
          <div v-if="course.publics" class="text-sm text-gray-700 mb-2">
            <span class="font-medium">Publics concernés :</span>
            <ul class="list-disc list-inside">
              <li v-for="p in course.publics" :key="p">{{ p }}</li>
            </ul>
          </div>

          <!-- Status banner (NEW, only for logged-in users with a status) -->
          <div
              v-if="regStatus"
              class="mt-3 rounded-xl border px-4 py-3 text-sm"
              :class="regStatusClass"
          >
            {{ regStatus.text }}
          </div>

          <!-- ACTIONS + SLIDING FORMS (UPDATED) -->
          <div class="mt-3 flex flex-col gap-3">
            <!-- Register button (shown if user not logged in, or if logged in & registration === 'none') -->
            <template v-if="canRegister">
              <button
                  class="bg-[var(--color-amf-primary)] text-white font-bold px-5 py-2 rounded-lg shadow hover:bg-[var(--color-amf-primary-dark)] transition"
                  @click="toggleRegister"
              >
                JE SOUHAITE M'INSCRIRE
              </button>

              <!-- Register sliding form -->
              <Transition name="slide-y">
                <div
                    v-show="showRegisterForm"
                    class="overflow-hidden border border-blue-200 bg-blue-50 rounded-xl p-4"
                >
                  <template v-if="isAuthenticated">
                    <p class="text-sm text-gray-800 mb-3">
                      Merci de votre intérêt pour cette formation. Veuillez confirmer que vous souhaitez vous inscrire
                      en cliquant sur le bouton ci-dessous.
                      Notre équipe vous contactera pour confirmer votre inscription. Si la formation est déjà complète,
                      vous serez placé(e) sur liste d’attente.
                    </p>
                    <button
                        class="w-full sm:w-auto bg-[var(--color-amf-primary)] text-white font-semibold px-4 py-2 rounded-lg shadow hover:bg-[var(--color-amf-primary-dark)]"
                        @click="confirmRegister"
                    >
                      Confirmer et m’inscrire
                    </button>
                  </template>
                  <template v-else>
                    <p class="text-sm text-gray-800 mb-3">
                      Pour vous inscrire, veuillez vous connecter ou créer un compte. Vous serez ensuite redirigé(e)
                      vers cette page.
                    </p>
                    <button
                        class="w-full sm:w-auto bg-[var(--color-amf-primary)] text-white font-semibold px-4 py-2 rounded-lg shadow hover:bg-[var(--color-amf-primary-dark)]"
                        @click="router.push({ name: 'Login', query: { redirect: route.fullPath } })"
                    >
                      Se connecter / créer un compte
                    </button>
                  </template>
                </div>
              </Transition>
            </template>

            <!-- Cancel + See activities (shown if canCancel or participant) -->
            <template v-if="canCancel || registration === 'participant'">
              <div class="flex flex-col sm:flex-row gap-2">
                <button
                    v-if="canCancel"
                    class="bg-red-950 text-white font-bold px-5 py-2 rounded-lg shadow hover:bg-red-700 transition"
                    @click="toggleCancel"
                >
                  Annuler mon inscription
                </button>

                <router-link
                    v-if="registration === 'participant'"
                    to="/app/mes-formations"
                    class="inline-flex items-center justify-center bg-gray-100 text-gray-900 font-semibold px-5 py-2 rounded-lg shadow hover:bg-gray-200 transition"
                >
                  Voir mes activités dans Mes Formations
                </router-link>
              </div>

              <!-- Cancel sliding form -->
              <Transition name="slide-y">
                <div
                    v-show="showCancelForm"
                    class="overflow-hidden border border-red-200 bg-red-50 rounded-xl p-4"
                >
                  <p class="text-sm text-gray-800 mb-3">
                    Veuillez confirmer que vous souhaitez annuler votre inscription.
                  </p>
                  <button
                      class="w-full sm:w-auto bg-red-900 text-white font-semibold px-4 py-2 rounded-lg shadow hover:bg-red-700"
                      @click="confirmCancel"
                  >
                    Confirmer l’annulation
                  </button>
                </div>
              </Transition>
            </template>
          </div>
        </div>

        <!-- Intervenants & Tag (right) -->
        <div class="flex flex-col items-end gap-2 min-w-[190px]">
          <div class="bg-blue-50 border border-blue-200 rounded-xl px-4 py-3 text-sm w-full">
            <div class="font-bold mb-1 text-2xl">Experts</div>
            <div v-for="interv in experts" :key="interv.name" class="flex gap-4 mb-2">
              <div class="flex flex-col items-start gap-2 max-w-2xs">
                <span class="font-semibold">{{ interv.civilite }} {{ interv.nom }} {{ interv.prenom }}</span>
                <span class="text-xs font-normal ml-2">{{ interv.profession }}: {{ interv.specialite }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Image + Description -->
      <div class="flex flex-col md:flex-row gap-8 my-6">
        <div class="md:w-2/5 flex-shrink-0">
          <img
              :src="imageUrl(course)"
              class="rounded-xl shadow-md w-full object-cover max-h-56"
              alt="Visuel formation"
          />
        </div>
        <div class="md:w-3/5 flex flex-col">
          <h2 class="text-xl font-semibold mb-2">{{ course.libellestage || course.name }}</h2>
          <div class="text-gray-800 mb-2 whitespace-pre-line" v-html="obs"></div>
        </div>
      </div>

      <!-- Steps / Stages -->
      <section v-if="steps && steps.length" class="my-8">
        <h3 class="text-lg font-semibold mb-3">Les étapes de cette formation</h3>
        <div class="grid md:grid-cols-3 gap-6">
          <div v-for="(step, i) in steps" :key="i" class="bg-blue-50 rounded-lg p-4 shadow-sm">
            <div class="text-blue-800 font-bold mb-1 uppercase">ÉTAPE {{ i + 1 }}</div>
            <div class="text-gray-900" v-html="step"></div>
          </div>
        </div>
      </section>
    </main>

    <!-- FOOTER -->
    <SiteFooter/>
  </div>
</template>

<style scoped>
/* Slide-down/up transition */
.slide-y-enter-active,
.slide-y-leave-active {
  transition: all 200ms ease;
}

.slide-y-enter-from,
.slide-y-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-4px);
}

.slide-y-enter-to,
.slide-y-leave-from {
  max-height: 320px; /* big enough for the small forms */
  opacity: 1;
  transform: translateY(0);
}
</style>
