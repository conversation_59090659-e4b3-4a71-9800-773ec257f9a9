<script setup lang="ts">
import { ref } from 'vue'
import { sendContact } from '../services'

const form = ref({ name:'', prenom:'', tel:'', email:'', message:'' })
const honeypot = ref('')
const loading = ref(false)
const status = ref('')

async function submit () {
  loading.value = true
  status.value = ''
  try {
    await sendContact({ ...form.value, hp: honeypot.value })
    status.value = 'ok'
    Object.keys(form.value).forEach(k => form.value[k] = '')
    honeypot.value = ''
  } catch (e: any) {
    status.value = (e?.response?.data?.error) || 'Échec de l’envoi.'
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <section class="contact-section" id="contact">
    <h2>Nous contacter</h2>
    <p>Questions, inscription, suggestion ? L’équipe AMF Formation & Évaluation est à votre écoute.</p>
    <div class="contact-form-wrapper">
      <div class="contact-flex">
        <form class="contact-form" @submit.prevent="submit">
          <!-- Honeypot (hidden) -->
          <input v-model="honeypot" class="hp" autocomplete="off" tabindex="-1" aria-hidden="true" />

          <div>
            <label for="name">Nom *</label>
            <input id="name" required type="text" placeholder="Votre nom" v-model.trim="form.name" />
          </div>
          <div>
            <label for="prenom">Prénom *</label>
            <input id="prenom" required type="text" placeholder="Votre prénom" v-model.trim="form.prenom" />
          </div>
          <div>
            <label for="tel">Téléphone *</label>
            <input id="tel" required type="tel" placeholder="06 00 00 00 00"
                   v-model.trim="form.tel" inputmode="tel" />
          </div>
          <div>
            <label for="email">Email *</label>
            <input id="email" required type="email" placeholder="<EMAIL>"
                   v-model.trim="form.email" />
          </div>
          <div class="full">
            <label for="message">Message</label>
            <textarea id="message" rows="2" placeholder="Votre message" v-model.trim="form.message"></textarea>
          </div>
          <button type="submit" :disabled="loading">
            <span v-if="!loading">Envoyer <i class="fa fa-paper-plane"></i></span>
            <span v-else>Envoi…</span>
          </button>

          <p v-if="status==='ok'" style="color:#2e7d32;margin:0">Merci, votre message a été envoyé.</p>
          <p v-else-if="status" style="color:#b00020;margin:0">{{ status }}</p>
        </form>
      </div>
    </div>
  </section>
</template>

<style scoped>
.hp { position:absolute; left:-9999px; opacity:0; height:0; width:0; }

.contact-section {
  background: #fff;
  padding: 4rem 1rem 3.2rem 1rem;
}
.contact-section h2 {
  text-align: center;
  margin-bottom: 1rem;
  font-size: 1.45rem;
  font-weight: 700;
  color: var(--amf-accent, #003049);
}
.contact-section p {
  text-align: center;
  color: #888;
  margin-bottom: 2.4rem;
}
.contact-form-wrapper {
  max-width: 800px;
  margin: 0 auto;
  background: #f6f9fa;
  padding: 2.2rem 2rem;
  border-radius: var(--amf-radius, 18px);
  box-shadow: 0 3px 22px rgba(0,137,145,0.07);
}
.contact-flex {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: space-between;
}
.contact-form {
  flex: 2 1 340px;
  display: grid;
  gap: 1.2rem 1.5rem;
  grid-template-columns: 1fr 1fr;
  margin: 0 auto;
}
.contact-form label {
  font-size: 0.98rem;
  color: var(--amf-primary-dark, #005f73);
  font-weight: 500;
  margin-bottom: 0.2rem;
  display: block;
}
.contact-form input,
.contact-form textarea {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1.5px solid #dde2e5;
  border-radius: 6px;
  font-size: 1rem;
  outline: none;
  background: #fff;
  resize: none;
  font-family: inherit;
  transition: border .17s;
}
.contact-form input:focus,
.contact-form textarea:focus {
  border-color: var(--amf-primary, #008891);
}
.contact-form .full {
  grid-column: 1/3;
}
.contact-form button {
  grid-column: 1/3;
  background: var(--amf-primary, #008891);
  color: #fff;
  font-weight: 700;
  border: none;
  border-radius: 7px;
  padding: 1rem;
  margin-top: 0.7rem;
  font-size: 1.06rem;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  justify-content: center;
}
.contact-form button:hover {
  background: var(--amf-primary-dark, #005f73);
}
/* .contact-info { ... }  <-- you may safely delete this rule */
@media (max-width: 850px) {
  .contact-flex { flex-direction: column; gap: 1.2rem; }
}
@media (max-width: 600px) {
  .contact-form-wrapper { padding: 1.3rem 0.6rem; }
  .contact-form { grid-template-columns: 1fr; }
  .contact-form .full,
  .contact-form button { grid-column: 1; }
}
</style>