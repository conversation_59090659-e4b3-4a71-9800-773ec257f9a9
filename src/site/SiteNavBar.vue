<template>
  <header class="amf-navbar sticky top-0 z-50 bg-white shadow">
    <nav class="max-w-7xl mx-auto flex items-center justify-between px-4 md:px-8 py-3 md:py-4 transition-all duration-150" aria-label="Main Navigation">
      <!-- Logo -->
      <router-link
        to="/"
        class="amf-navbar-logo flex-shrink-0 -ml-8 group"
        aria-label="Accueil"
      >
        <img
          src="/images/logo.png"
          alt="AMF Formation & Evaluation"
          class="p-2 h-6 md:h-10 w-auto rounded-xl transition-all duration-200 border-2 border-transparent group-hover:ring-1 group-hover:ring-cyan-500"
          style="object-fit:contain"
        />
      </router-link>

      <!-- Hamburger (Mobile) -->
      <button
        class="md:hidden p-2 ml-3"
        @click="menuOpen = !menuOpen"
        aria-label="Ouvrir le menu"
      >
        <i class="fa" :class="menuOpen ? 'fa-times' : 'fa-bars'"></i>
      </button>

      <!-- Main Nav -->
      <div class="amf-nav-links hidden md:flex flex-1 justify-center gap-2 md:gap-6 xl:gap-8">
        <router-link to="/formations" class="amf-nav-link" :class="{ 'router-link-active': $route.path === '/formations' }">Nos Formations</router-link>
        <!-- <router-link :to="{ path: '/', hash: '#usp' }" class="amf-nav-link">Classe Virtuelle</router-link-->
        <router-link :to="{ path: '/', hash: '#team' }" class="amf-nav-link">Équipe</router-link>
        <router-link :to="{ path: '/', hash: '#contact' }" class="amf-nav-link">Contact</router-link>
        <!-- Authenticated user quick links -->
        <template v-if="isAuthenticated">
          <router-link v-if="hasCourses || hasInterventions" to="/app/mes-activites" class="amf-nav-link"><i class="fa fa-user-circle mr-1"></i>Mes activités</router-link>
          <!--router-link v-if="hasCourses" to="/app/mes-formations" class="amf-nav-link"><i class="fa fa-graduation-cap mr-1"></i>Mes formations</router-link>
          <router-link v-if="hasInterventions" to="/app/mes-interventions" class="amf-nav-link"><i class="fa fa-stethoscope mr-1"></i>Mes interventions</router-link-->
        </template>
      </div>

      <!-- User menu (desktop) -->
      <div class="flex items-center">
        <router-link
          v-if="!isAuthenticated"
          to="/login"
          class="amf-navbar-cta flex items-center gap-2 px-4 py-2 rounded-lg font-semibold bg-[var(--amf-primary,#008891)] text-white hover:bg-[var(--amf-primary-dark,#005f73)] shadow transition focus:outline-none focus:ring-2 focus:ring-primary"
          aria-label="Se connecter"
        >
          <i class="fa fa-user"></i>
          <span class="hidden sm:inline">Se connecter</span>
        </router-link>
        <div
          v-else
          class="relative ml-2"
          @mouseenter="userDropdown = true"
          @mouseleave="userDropdown = false"
        >
          <button
            class="flex items-center gap-2 px-4 py-2 rounded-lg transition-colors
              hover:bg-[var(--amf-primary,#008891)] hover:text-white
              focus:outline-none focus:ring-1 focus:ring-[var(--amf-primary,#008891)] focus:ring-opacity-10
              h-10"
            @click="userDropdown = !userDropdown"
            aria-haspopup="true"
            :aria-expanded="userDropdown"
            type="button"
          >
            <template v-if="user && user.photoURL">
              <img
                :src="user.photoURL"
                class="w-9 h-9 rounded-full border border-gray-200 shadow"
                alt="Compte utilisateur"
              />
            </template>
            <template v-else>
              <div class="w-9 h-9 rounded-full flex items-center justify-center font-bold text-lg">
                <i class="fa fa-user"></i>
              </div>
            </template>
            <i class="fa fa-chevron-down text-sm"></i>
          </button>
          <!-- Dropdown -->
          <transition name="fade">
            <div
              v-if="userDropdown"
              class="absolute right-0 mt-0 w-48 bg-white rounded-lg shadow-lg py-2 z-30 border border-gray-100"
              @mouseenter="userDropdown = true"
              @mouseleave="userDropdown = false"
            >
              <div class="px-4 py-2 text-gray-800 font-medium border-b">{{ user.displayName || user.email }}</div>
              <!--router-link to="/app/account" class="block px-4 py-2 hover:amf-bg-primary hover:text-white transition flex items-center gap-2">
                <i class="fa fa-user-circle"></i> Mon compte
              </router-link-->
              <router-link v-if="hasCourses || hasInterventions" to="/app/mes-activites" class="amf-nav-link"><i class="fa fa-user-circle mr-1"></i>Mes activités</router-link>
              <router-link v-if="hasCourses" to="/app/mes-formations" class="block px-4 py-2 hover:amf-bg-primary hover:text-white transition flex items-center gap-2">
                <i class="fa fa-graduation-cap"></i> Mes formations
              </router-link>
              <router-link v-if="hasInterventions" to="/app/mes-interventions" class="block px-4 py-2 hover:amf-bg-primary hover:text-white transition flex items-center gap-2">
                <i class="fa fa-stethoscope"></i> Mes interventions
              </router-link>
              <div class="border-t my-2"></div>
              <button @click="logout" class="block w-full text-left px-4 py-2 hover:bg-red-50 hover:text-red-700 cursor-pointer flex items-center gap-2">
                <i class="fa fa-sign-out-alt"></i> Déconnexion
              </button>
            </div>
          </transition>
        </div>
      </div>
    </nav>

    <!-- Mobile menu (drawer) -->
    <transition name="fade">
      <div v-if="menuOpen" class="md:hidden fixed inset-0 z-40 bg-black/40" @click.self="menuOpen = false">
        <nav class="fixed top-0 right-0 w-72 h-full bg-white shadow-lg p-7 flex flex-col gap-3">
          <router-link :to="{ path: '/', hash: '#catalog' }" class="amf-nav-link" @click="menuOpen = false">Nos Formations</router-link>
          <router-link :to="{ path: '/', hash: '#usp' }" class="amf-nav-link" @click="menuOpen = false">Classe Virtuelle</router-link>
          <router-link :to="{ path: '/', hash: '#team' }" class="amf-nav-link" @click="menuOpen = false">Équipe</router-link>
          <router-link :to="{ path: '/', hash: '#contact' }" class="amf-nav-link" @click="menuOpen = false">Contact</router-link>
          <template v-if="isAuthenticated">
            <div class="border-t my-2"></div>
            <router-link v-if="hasCourses" to="/app/mes-formations" class="amf-nav-link" @click="menuOpen = false"><i class="fa fa-graduation-cap mr-1"></i>Mes formations</router-link>
            <router-link v-if="hasInterventions" to="/app/mes-interventions" class="amf-nav-link" @click="menuOpen = false"><i class="fa fa-stethoscope mr-1"></i>Mes interventions</router-link>
            <router-link to="/app/account" class="amf-nav-link" @click="menuOpen = false"><i class="fa fa-user-circle mr-1"></i>Mon compte</router-link>
            <button @click="logout; menuOpen = false" class="amf-nav-link text-left text-red-700 hover:bg-red-50 mt-3 flex items-center gap-2">
              <i class="fa fa-sign-out-alt"></i> Déconnexion
            </button>
          </template>
          <template v-else>
            <router-link to="/login" class="amf-nav-link mt-4" @click="menuOpen = false"><i class="fa fa-user mr-1"></i> Se connecter</router-link>
          </template>
        </nav>
      </div>
    </transition>
  </header>
</template>


<script setup>
import { ref, computed, watch } from "vue";
import { getAuth, signOut } from "firebase/auth";
import { useCurrentUser } from "vuefire";
import { storeToRefs } from "pinia";

// ✅ Make sure these paths point to the *same* store files you defined
import { useMyCoursesStore } from "../user/courses/useRegistrations";
import { useInterventionsStore } from "../user/courses/useInterventions";
import router from "../router.js";

const menuOpen = ref(false);
const userDropdown = ref(false);

const user = useCurrentUser();
const isAuthenticated = computed(() => !!user.value);

const coursesStore = useMyCoursesStore();
const interventionsStore = useInterventionsStore();

// 🟢 Always normalize store fields with storeToRefs
//    (works for state *and* getters from both Options & Setup stores)
const {
  hasLoadedCourses: coursesLoaded = ref(false),
  isEmpty: coursesEmpty = ref(true),
  // If your courses store uses a different name (e.g. hasLoadedCourses),
  // expose it *as hasLoaded* in the store OR map it here:
  // hasLoadedCourses: coursesLoaded = ref(false),
} = storeToRefs(coursesStore);

const {
  hasLoaded: interventionsLoaded = ref(false),
  isEmpty: interventionsEmpty = ref(true),
} = storeToRefs(interventionsStore);

// 👇 Simple, robust booleans for the template
const hasCourses = computed(() => coursesLoaded.value && !coursesEmpty.value);
const hasInterventions = computed(() => interventionsLoaded.value && !interventionsEmpty.value);

// 🔑 Load stores as soon as user is authenticated.
//    `immediate: true` ensures we run once right away on mount.
watch(
  isAuthenticated,
  (authed) => {
    if (authed) {
      coursesStore.ensure();
      interventionsStore.ensure();
    } else {
      // Optional: clear on logout
      coursesStore.invalidate?.();
      interventionsStore.invalidate?.();
    }
  },
  { immediate: true }
);

function logout() {
  signOut(getAuth());
  userDropdown.value = false;
  menuOpen.value = false;
  coursesStore.invalidate?.();
  interventionsStore.invalidate?.();
  router.push({ name: "Accueil" });
}

// Close dropdown on click outside (desktop)
function onClickOutsideDropdown(event) {
  if (!event.target.closest(".relative")) {
    userDropdown.value = false;
  }
}
document.addEventListener("click", onClickOutsideDropdown);
</script>

<style scoped>
/* You can use Tailwind utility classes, but here are some CSS overrides for nav-link style */
.amf-nav-link {
  color: var(--amf-accent, #003049);
  font-weight: 500;
  font-size: 1.03rem;
  padding: 0.45rem 1.1rem;
  border-radius: 6px;
  transition: color 0.18s, background 0.18s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  position: relative;
}
.amf-nav-link:hover,
.amf-nav-link:focus {
  background: var(--amf-primary, #008891);
  color: #fff;
  outline: none;
}
.amf-navbar-cta {
  box-shadow: 0 2px 8px rgba(0,137,145,0.10);
  font-size: 1.05rem;
}
.fade-enter-active, .fade-leave-active { transition: opacity .18s }
.fade-enter-from, .fade-leave-to { opacity: 0 }
</style>
