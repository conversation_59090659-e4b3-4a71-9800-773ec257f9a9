<template>
  <div>
    <SiteNavBar />
    <div class="amf-text-page">

            <h1 class="pb-4 mb-4 fst-italic border-bottom">Mentions légales</h1>
            <div class="row g-5">
                <div class="col-lg-10 ">
                    <h4  class="section-heading text-uppercase"><strong >AMF Formation et évaluation</strong></h4>
                    <h6>15 rue Guyton de Morveau</h6>
                    <h6>75013 Paris - FRANCE</h6>
                    <h6>Tél : +33 (0)1 45 88 05 05</h6>
                    <h6>Email : <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a> </h6>
                </div>

            </div>

            <!--div class="row g-5 py-5">
                <div class="col-lg-10 ">
                    <h5><strong style="color: #fed136 ">Directeur de la publication</strong></h5>
                    <h6>Dr. <PERSON>, président d'AMF Formation et Évaluation</h6>
                </div>
            </div-->

            <div class="row g-5 ">
                <div class="col-lg-10 ">
                    <h5><strong >Responsable éditorial</strong></h5>
                    <h6>Pour toute suggestion, information, réaction concernant le contenu rédactionnel contactez : <a href="mailto:<EMAIL>" ><EMAIL></a></h6>
                </div>
            </div>

            <div class="row g-5 ">
                <div class="col-lg-10 ">
                    <h5><strong >Responsable informatique</strong></h5>
                    <h6>Pour toute question concernant le fonctionnement du site internet contactez : <a href="mailto:<EMAIL>" ><EMAIL></a></h6>
                </div>
            </div>

            <div class="row g-5 ">
                <div class="col-lg-10 ">
                    <h5><strong >Hébergement web</strong></h5>
                    <h6>Société OVH</h6>
                    <h6>2 rue Kellermann </h6>
                    <h6>59100 Roubaix – FRANCE</h6>
                </div>
            </div>

        </div>
    <SiteFooter />
  </div>
</template>

<script setup>
import SiteNavBar from './SiteNavBar.vue'
import SiteFooter from './SiteFooter.vue'
</script>
