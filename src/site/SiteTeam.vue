<script setup lang="ts">
// import {LOGO_NAME} from "../config.js"; // Not used here
</script>

<template>
  <section class="team-section" id="team">
    <div class="section-title">Notre équipe</div>
    <div class="team-row">
      <div class="team-card">
        <img src="/images/associations/abraham.png" alt="Dr <PERSON>">
        <h5>Dr <PERSON></h5>
        <div class="role">Généraliste – Président</div>
        <p>Président de l’association et référent pédagogique.</p>
      </div>
      <div class="team-card">
        <img src="/images/associations/aurore.JPG" alt="Dr <PERSON><PERSON><PERSON>">
        <h5>Dr <PERSON><PERSON><PERSON></h5>
        <div class="role">Généraliste – Référente</div>
        <p>Spécialiste de la formation médicale continue.</p>
      </div>
      <div class="team-card">
        <img src="/images/associations/sebban.png" alt="Dr <PERSON>">
        <h5>Dr <PERSON></h5>
        <div class="role">Pédiatre – Référent</div>
        <p>Pédiatre et responsable formation pour la pédiatrie.</p>
      </div>
      <div class="team-card">
        <img src="/images/associations/hanen.JPG" alt="Mme Hanen Ayari">
        <h5>Mme Hanen Ayari</h5>
        <div class="role">Référente handicap</div>
        <p>Accompagnement et accessibilité pour tous les apprenants.</p>
      </div>
    </div>
  </section>
</template>

<style scoped>
.team-section {
  padding: 4rem 1rem 3rem 1rem;
  background: var(--amf-bg, #f5fafd);
}
.section-title {
  font-size: 1.45rem;
  font-weight: 700;
  margin-bottom: 2.2rem;
  text-align: center;
  letter-spacing: -0.5px;
  color: var(--amf-accent, #003049);
}
.team-row {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 2.2rem;
  flex-wrap: wrap;
  justify-content: center;
}
.team-card {
  background: #fff;
  border-radius: var(--amf-radius, 18px);
  box-shadow: var(--amf-shadow, 0 4px 18px rgba(0,0,0,0.06));
  padding: 2rem 1.2rem 1.3rem 1.2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 210px;
  margin-bottom: 1.2rem;
  transition: box-shadow .15s, transform .12s;
}
.team-card:hover {
  box-shadow: 0 4px 20px rgba(0,137,145,0.18);
  transform: translateY(-4px) scale(1.015);
}
.team-card img {
  width: 98px; height: 98px;
  border-radius: 50%;
  margin-bottom: 1rem;
  object-fit: cover;
  box-shadow: 0 2px 9px rgba(0,137,145,0.10);
}
.team-card h5 {
  font-size: 1.08rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
  color: var(--amf-primary-dark, #005f73);
}
.team-card .role {
  background: var(--amf-primary-light, #e0f4fb);
  color: var(--amf-primary, #008891);
  font-size: 0.81rem;        /* smaller font */
  padding: 0.18rem 0.5rem;   /* slightly tighter padding */
  border-radius: 99px;
  margin-bottom: 0.7rem;
  margin-top: 0.1rem;
  font-weight: 500;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 175px;          /* wider max-width for pills */
}

@media (max-width: 900px) {
  .team-row { gap: 1.1rem; }
  .team-card .role { font-size: 0.82rem; } /* Even smaller on medium screens */
}
@media (max-width: 600px) {
  .team-row { flex-direction: column; align-items: center; }
  .team-section { padding: 2rem 0.3rem 2rem 0.3rem; }
  .team-card .role { font-size: 0.78rem; } /* Smallest font size on mobile */
}
.team-card p {
  color: #555;
  font-size: 0.98rem;
  margin-bottom: 0;
  opacity: 0.89;
}
</style>