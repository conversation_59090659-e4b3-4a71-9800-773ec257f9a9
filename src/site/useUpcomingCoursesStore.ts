// stores/courses.ts
import { defineStore } from 'pinia'
import { getUpcomingCourses } from '../services'
import { get as idbGet, set as idbSet } from 'idb-keyval'
import { loadStageImage } from '../formation'

const TTL_MS = 5 * 60 * 1000 // 5 minutes

type Status = 'idle' | 'loading' | 'ready' | 'error'

export const useUpcomingCoursesStore = defineStore('upcoming-courses', {
  state: () => ({
    items: [] as any[],
    lastFetched: 0,
    status: 'idle' as Status,
    error: null as Error | null,
    _inFlight: null as Promise<any> | null,

    // Only needed if loadStageImage sometimes returns Blobs → object URLs
    _objectUrls: [] as string[],
  }),

  getters: {
    filtered: (state) => (f: string) => {
      if (!f) return state.items
      if (f === 'FAF-PM') return state.items.filter(c => c.organisme === 'FAF')
      if (f === 'DPC')    return state.items.filter(c => c.organisme === 'ANDPC')
      return state.items.filter(c => c.organisme && !['FAF','ANDPC'].includes(c.organisme))
    }
  },

  actions: {
    /**
     * Prepare images for a list of courses in parallel and set `course.image`.
     * - If loadStageImage returns a string URL, use it.
     * - If it returns a Blob, make an object URL (and remember it for cleanup).
     * - On error, fall back to original `image_url` (so the browser can try).
     */
    async _withImages(items: any[]) {
      const prepared = await Promise.all(items.map(async (c) => {
        try {
          const result = await loadStageImage(c.image_url)

          // URL string
          if (typeof result === 'string' && result) {
            return { ...c, image: result }
          }

          // Blob → object URL
          if (result instanceof Blob) {
            const objUrl = URL.createObjectURL(result)
            this._objectUrls.push(objUrl)
            return { ...c, image: objUrl }
          }

          // Unexpected type → fallback
          return { ...c, image: c.image ?? c.image_url ?? null }
        } catch {
          // On failure, still provide something to display
          return { ...c, image: c.image ?? c.image_url ?? null }
        }
      }))

      return prepared
    },

    async ensure({ force = false } = {}) {
      if (this._inFlight) return this._inFlight

      const fresh = Date.now() - this.lastFetched < TTL_MS
      if (!force && fresh && this.items.length) return this.items

      this.status = 'loading'

      this._inFlight = (async () => {
        try {
          // 1) Fast hydrate from IDB (may already include `image`)
          if (!force && !this.items.length) {
            const cached = await idbGet<{ ts: number; items: any[] }>('upcomingCourses')
            if (cached && Date.now() - cached.ts < TTL_MS) {
              // If cached items are missing images, prepare them now.
              const needsImages = cached.items.some(i => !i.image)
              this.items = needsImages ? await this._withImages(cached.items) : cached.items
              this.lastFetched = cached.ts
              this.status = 'ready'
            }
          }

          // 2) Revalidate from network and prepare images in parallel
          const raw = await getUpcomingCourses()
          const withImages = await this._withImages(raw)

          this.items = withImages
          this.lastFetched = Date.now()
          await idbSet('upcomingCourses', { ts: this.lastFetched, items: withImages })
          this.status = 'ready'
          return withImages
        } catch (e: any) {
          this.error = e
          this.status = 'error'
          return this.items // return whatever we have (possibly cached)
        } finally {
          this._inFlight = null
        }
      })()

      return this._inFlight
    },

    /** Optional: call on logout/app destroy if you used Blob → object URLs */
    revokeObjectUrls() {
      for (const url of this._objectUrls) URL.revokeObjectURL(url)
      this._objectUrls = []
    }
  }
})
