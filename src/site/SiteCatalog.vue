<template>
  <section class="catalog-section" id="catalog">
    <div class="section-title">Parcourez notre catalogue de formations</div>
    <!--div class="categories-row">
      <div class="category-card"><i class="fa fa-user-md"></i> Médecins</div>
      <div class="category-card"><i class="fa fa-pills"></i> Pharmaciens</div>
      <div class="category-card"><i class="fa fa-hospital"></i> Officines</div>
    </div-->
    <div class="courses-grid">
      <div class="course-card">
        <img src="/images/associations/OrgaFAF.png" alt="Formations du FAF-PM">
        <div class="course-card-body">
          <div class="course-card-title">Formations du FAF-PM</div>
          <div class="course-card-desc">Sessions pratiques et interactives pour médecins généralistes et spécialistes.</div>
          <div class="course-card-footer">
            <router-link
              :to="{ name: 'CoursesPage', query: { filter: 'FAF-PM' } }"
            >
              Voir plus
            </router-link>
          </div>
        </div>
      </div>
      <div class="course-card">
        <img src="/images/associations/OrgaDPC.png" alt="Formations DPC">
        <div class="course-card-body">
          <div class="course-card-title">Formations DPC</div>
          <div class="course-card-desc">E-learning et ateliers pour la montée en compétences, éligible à la prise en charge DPC.</div>
          <div class="course-card-footer">
            <router-link
              :to="{ name: 'CoursesPage', query: { filter: 'DPC' } }"
            >
              Voir plus
            </router-link>
          </div>
        </div>
      </div>
      <div class="course-card">
        <img src="/images/associations/OrgaAutres.png" alt="Autres formations">
        <div class="course-card-body">
          <div class="course-card-title">Autres formations</div>
          <div class="course-card-desc">Thématiques variées et formations sur-mesure pour tous professionnels de santé.</div>
          <div class="course-card-footer">
            <router-link
              :to="{ name: 'CoursesPage', query: { filter: 'Autres' } }"
            >
              Voir plus
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.catalog-section {
  max-width: 1180px;
  margin: 0 auto 3.5rem auto;
  padding: 0 1rem;
}
.section-title {
  font-size: 1.55rem;
  font-weight: 700;
  margin: 1.7rem 0 1.7rem 0;
  letter-spacing: -0.5px;
  text-align: center;
  color: var(--amf-accent, #003049);
}
.categories-row {
  display: flex;
  justify-content: center;
  gap: 1.7rem;
  margin-bottom: 2.2rem;
  flex-wrap: wrap;
}
.category-card {
  background: var(--amf-primary, #008891);
  color: #fff;
  padding: 1.2rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1.08rem;
  box-shadow: 0 2px 8px rgba(0,137,145,0.07);
  cursor: pointer;
  display: flex; align-items: center; gap: 0.7rem;
  transition: background 0.2s, transform 0.15s;
}
.category-card i {
  font-size: 1.3rem;
  color: #fff9;
}
.category-card:hover {
  background: var(--amf-primary-dark, #005f73);
  transform: translateY(-3px) scale(1.03);
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px,1fr));
  gap: 2rem;
  margin-bottom: 0.8rem;
}
.course-card {
  background: var(--amf-card-bg, #fff);
  border-radius: var(--amf-radius, 18px);
  box-shadow: var(--amf-shadow, 0 4px 18px rgba(0,0,0,0.06));
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform .16s, box-shadow .16s;
}
.course-card:hover {
  transform: translateY(-6px) scale(1.022);
  box-shadow: 0 4px 26px rgba(0,137,145,0.14);
}
.course-card img {
  width: 100%; height: 160px; object-fit: cover;
  background: var(--amf-primary-light, #e0f4fb);
  display: block;
  border-radius: 0 0 0 0;
}
.course-card-body {
  padding: 1.1rem 1rem 1.2rem 1rem;
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  gap: 0.7rem;
}
.course-card-title {
  font-size: 1.14rem;
  font-weight: 700;
  color: var(--amf-primary-dark, #005f73);
  margin-bottom: 0.17rem;
}
.course-card-desc {
  color: #334155;
  font-size: 1rem;
  opacity: 0.88;
  flex: 1 1 auto;
}
.course-card-footer {
  margin-top: 0.7rem;
  display: flex; justify-content: flex-end;
}
.course-card-footer a {
  background: var(--amf-primary, #008891);
  color: #fff;
  padding: 0.6rem 1.3rem;
  font-weight: 600;
  border-radius: 7px;
  font-size: 0.98rem;
  transition: background 0.18s;
}
.course-card-footer a:hover {
  background: var(--amf-primary-dark, #005f73);
}

/* Responsive tweaks */
@media (max-width: 800px) {
  .categories-row { gap: 1rem; }
  .courses-grid { gap: 1.1rem; }
}
@media (max-width: 600px) {
  .section-title { font-size: 1.19rem; }
  .categories-row { flex-direction: column; align-items: center; gap: 0.7rem; }
  .courses-grid { grid-template-columns: 1fr; gap: 0.6rem; }
}
</style>
