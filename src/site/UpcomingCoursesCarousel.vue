<template>
  <section class="w-full py-8 bg-transparent relative z-10 mt-16">
    <div class="max-w-5xl mx-auto">
      <h3 class="text-2xl md:text-3xl font-bold mb-8 text-center text-[var(--color-amf-primary)] tracking-tight">
        Prochaines formations à venir
      </h3>
      <div class="relative">
        <!-- Carousel Container -->
        <div class="overflow-hidden">
          <div
            class="flex transition-transform duration-500 ease-out justify-center"
            :style="{ transform: `translateX(-${activeIndex * 100}%)` }"
            ref="carouselTrack"
          >
            <div
              v-for="course in courses"
              :key="course.session_id"
              class="min-w-full sm:min-w-[24rem] md:min-w-[26rem] lg:min-w-[28rem] px-3 flex justify-center"
            >
              <div
                class="amf-bg-card amf-rounded amf-shadow border border-[var(--color-amf-primary-light)] p-8 flex flex-col h-full
                  hover:scale-105 hover:shadow-xl transition-transform duration-300 ease-out"
              >
                <div class="flex items-center gap-4 mb-3">
                  <img
                    v-if="course.cover"
                    :src="course.cover"
                    class="w-16 h-16 rounded-lg object-cover amf-bg-primary-light"
                    alt="Visuel formation"
                  />
                  <div>
                    <h4 class="text-xl font-extrabold text-[var(--color-amf-primary)] leading-tight mb-1">
                      {{ course.course_head?.libellestage || course.title }}
                    </h4>
                    <div class="text-sm text-gray-500">
                      {{ formatDate(course.start_date) }} &ndash; {{ formatDate(course.end_date) }}
                    </div>
                  </div>
                </div>
                <div class="text-gray-700 text-base flex-1 mb-3 line-clamp-3">
                  {{ course.course_head?.notes || 'Découvrez cette formation en ligne, interactive et à jour.' }}
                </div>
                <div class="flex items-center gap-3 mt-auto mb-6">
                  <span class="inline-flex items-center px-2 py-1 rounded bg-[var(--color-amf-primary-light)] text-[var(--color-amf-primary)] text-xs font-semibold">
                    {{ course.course_head?.organisme || 'AMF' }}
                  </span>
                  <span class="inline-flex items-center px-2 py-1 rounded bg-gray-100 text-gray-600 text-xs">
                    {{ course.course_head?.location_name || 'En ligne' }}
                  </span>
                </div>
                <button
                  class="amf-bg-primary hover:amf-bg-primary-dark text-white font-bold w-full py-3 rounded-lg shadow transition text-lg"
                  @click="$emit('courseClick', course)"
                >
                  Voir la formation
                </button>
              </div>
            </div>
          </div>
        </div>
        <!-- Controls -->
        <button
          class="absolute -left-7 top-1/2 -translate-y-1/2 bg-white/90 border border-[var(--color-amf-primary-light)] rounded-full shadow-lg w-12 h-12 flex items-center justify-center hover:bg-[var(--color-amf-primary-light)] transition z-20"
          @click="prev"
          :disabled="activeIndex === 0"
          aria-label="Précédent"
        >
          <i class="fa fa-chevron-left text-2xl text-[var(--color-amf-primary)]"></i>
        </button>
        <button
          class="absolute -right-7 top-1/2 -translate-y-1/2 bg-white/90 border border-[var(--color-amf-primary-light)] rounded-full shadow-lg w-12 h-12 flex items-center justify-center hover:bg-[var(--color-amf-primary-light)] transition z-20"
          @click="next"
          :disabled="activeIndex === courses.length - 1"
          aria-label="Suivant"
        >
          <i class="fa fa-chevron-right text-2xl text-[var(--color-amf-primary)]"></i>
        </button>
      </div>
      <!-- Dots -->
      <div class="flex justify-center mt-6 gap-3">
        <button
          v-for="(_, idx) in courses"
          :key="idx"
          class="w-3 h-3 rounded-full border border-[var(--color-amf-primary-light)] transition"
          :class="activeIndex === idx
            ? 'bg-[var(--color-amf-primary)]'
            : 'bg-gray-200 hover:bg-[var(--color-amf-primary-light)]'"
          @click="goTo(idx)"
        ></button>
      </div>
    </div>
  </section>
</template>


<script setup>
import { ref } from 'vue'

// Props: Pass a list of course objects with needed fields!
defineProps({
  courses: {
    type: Array,
    required: true,
    default: () => []
  }
})

const activeIndex = ref(0)

function next() {
  if (activeIndex.value < courses.length - 1) activeIndex.value++
}
function prev() {
  if (activeIndex.value > 0) activeIndex.value--
}
function goTo(idx) {
  activeIndex.value = idx
}
function formatDate(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', { day: '2-digit', month: 'short', year: 'numeric' })
}
</script>

<style scoped>
/* Clamp to three lines if supported, otherwise fallback is just regular text */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
