<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  name: { type: String, default: '' },
  address1: { type: String, default: '' },
  address2: { type: String, default: '' },
  postalCode: { type: String, default: '' },
  city: { type: String, default: '' },
  region: { type: String, default: '' },
  country: { type: String, default: 'France' },
  lat: { type: [Number, String], default: null },
  lng: { type: [Number, String], default: null },
  isOnline: { type: Boolean, default: false },
  onlineLabel: { type: String, default: 'AMF Formation en Ligne et en Direct' },
  provider: { type: String, default: 'google' }, // 'google' | 'osm'
  showMapLink: { type: Boolean, default: true },
})

const cityLine = computed(() => [props.postalCode, props.city].filter(Boolean).join(' '))

const items = computed(() => {
  if (props.isOnline) return [{ icon: '💻', text: props.onlineLabel }]
  const arr: Array<{icon:string,text:string}> = []
  if (props.name)       arr.push({ icon: '🏛️', text: props.name })
  if (props.address1)   arr.push({ icon: '📍', text: props.address1 })
  if (props.address2)   arr.push({ icon: '📫', text: props.address2 })
  if (cityLine.value)   arr.push({ icon: '🏙️', text: cityLine.value })
  if (props.region)     arr.push({ icon: '🗺️', text: props.region })
  if (props.country)    arr.push({ icon: '🌍', text: props.country })
  return arr
})

const hasAddress = computed(() => items.value.length > 0)

const mapsHref = computed(() => {
  if (props.isOnline || !props.showMapLink) return null

  if (props.lat != null && props.lng != null) {
    const lat = String(props.lat).trim()
    const lng = String(props.lng).trim()
    return props.provider === 'osm'
      ? `https://www.openstreetmap.org/?mlat=${encodeURIComponent(lat)}&mlon=${encodeURIComponent(lng)}#map=16/${encodeURIComponent(lat)}/${encodeURIComponent(lng)}`
      : `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(lat+','+lng)}`
  }

  if (!hasAddress.value) return null

  const q = [
    props.name,
    props.address1,
    props.address2,
    cityLine.value,
    props.region,
    props.country || 'France',
  ].filter(Boolean).join(', ')

  if (!q.trim()) return null

  return props.provider === 'osm'
    ? `https://www.openstreetmap.org/search?query=${encodeURIComponent(q)}`
    : `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(q)}`
})
</script>

<template>
  <!-- Compact, modern card with Tailwind -->
  <div class="flex items-center justify-between gap-3 rounded-xl border border-gray-200 bg-white p-3 text-sm">
    <!-- Left stack: pin + lines -->
    <div class="flex min-w-0 items-start gap-3">
      <div class="grid h-8 w-8 place-items-center rounded-lg bg-indigo-50 text-base">📍</div>

      <div class="grid min-w-0 gap-1">
        <template v-if="hasAddress">
          <div v-for="(it, i) in items" :key="i" class="flex min-w-0 items-center gap-2">
            <span class="grid h-5 w-5 place-items-center rounded bg-gray-100 text-[11px]" aria-hidden="true">{{ it.icon }}</span>
            <span class="truncate text-gray-700">{{ it.text }}</span>
          </div>
        </template>
        <div v-else class="text-gray-500">Adresse non disponible</div>
      </div>
    </div>

    <!-- Right: map button -->
    <a
      v-if="mapsHref"
      :href="mapsHref"
      target="_blank"
      rel="noopener"
      :title="provider === 'osm' ? 'Ouvrir dans OpenStreetMap' : 'Ouvrir dans Google Maps'"
      :aria-label="provider === 'osm' ? 'Ouvrir dans OpenStreetMap' : 'Ouvrir dans Google Maps'"
      class="inline-flex h-9 w-9 items-center justify-center rounded-lg border border-gray-200 bg-gray-50 transition hover:bg-indigo-50 hover:shadow focus:outline-none focus:ring-2 focus:ring-indigo-300"
    >
      <!-- Simple map glyph (SVG) -->
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="h-5 w-5 text-gray-700">
        <path d="M9.5 3.5 3 6v14l6.5-2.5L15 21l6-2V5L15 7 9.5 3.5ZM9 7.74v10.52l-4 1.54V9.28l4-1.54Zm2-.77 4 2V21l-4-2V6.97Zm10 .03-4 1.33v11.27l4-1.33V7Z"/>
      </svg>
      <span class="sr-only">{{ provider === 'osm' ? 'Ouvrir dans OpenStreetMap' : 'Ouvrir dans Google Maps' }}</span>
    </a>
  </div>
</template>

<!-- No scoped CSS: all styling via Tailwind utilities -->
