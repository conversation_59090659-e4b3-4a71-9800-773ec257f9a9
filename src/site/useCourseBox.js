export function formatStartDate(dt) {
  if (!dt) return ''
  const d = new Date(dt)
  return d.toLocaleDateString('fr-FR', {
    weekday: 'long', day: '2-digit', month: 'long'
  }) + ' à ' + d.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })
}

export function imageUrl(course) {
  // Handles prop image, fallback, or construct URL from featured_image_id
  return (
    course?.image ||
    (course?.featured_image_id
      ? `/images/course_${course.featured_image_id}.jpg`
      : '/images/course_image.jpg')
  )
}