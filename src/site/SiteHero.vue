<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import { useUpcomingCoursesStore } from './useUpcomingCoursesStore.js'
import CarouselCourseBox from './CarouselCourseBox.vue'

const props = defineProps({
  height: { type: Number, default: 530 },
  compact: { type: Boolean, default: false }
})

const emit = defineEmits(['click'])

/** Store & derived data */
const store = useUpcomingCoursesStore()
const loading = computed(() => store.status === 'loading' && !store.items.length)
const upcomingCourses = computed(() => store.items) // courses with `image` set in the store
const isReady = computed(() => store.status === 'ready' && upcomingCourses.value.length > 0)
const currentCourse = computed(() => {
  const i = carouselIndex.value
  return i >= 0 ? upcomingCourses.value[i] : null
})

/** Carousel state */
const carouselIndex = ref(-1)
const carouselVisible = ref(true)
let intervalId /** @type {ReturnType<typeof setInterval> | null} */ = null

/** Carousel controls */
function advanceToNext() {
  const len = upcomingCourses.value.length
  if (!len) return
  carouselIndex.value = (carouselIndex.value + 1) % len
  carouselVisible.value = true
  restartCarousel()
}
function advanceToPrev() {
  const len = upcomingCourses.value.length
  if (!len) return
  carouselIndex.value = (carouselIndex.value - 1 + len) % len
  carouselVisible.value = true
  restartCarousel()
}
function advanceCarousel() {
  const len = upcomingCourses.value.length
  if (!len) { carouselIndex.value = -1; return }
  carouselIndex.value = (carouselIndex.value + 1) % len
  carouselVisible.value = true
}
function startCarousel() {
  stopCarousel()
  intervalId = setInterval(advanceCarousel, 5500)
}
function stopCarousel() {
  if (intervalId) { clearInterval(intervalId); intervalId = null }
}
function restartCarousel() { stopCarousel(); startCarousel() }
function goTo(i) {
  const len = upcomingCourses.value.length
  if (i < 0 || i >= len) return
  carouselIndex.value = i
  carouselVisible.value = true
  restartCarousel()
}
function closeCarousel() {
  carouselVisible.value = false
  stopCarousel()
}

/** Lifecycle */
onMounted(async () => {
  await store.ensure()
  if (isReady.value) {
    setTimeout(() => {
      carouselIndex.value = 0
      carouselVisible.value = true
      startCarousel()
    }, 600)
  }
})
watch(upcomingCourses, (list) => {
  if (!list?.length) { stopCarousel(); carouselIndex.value = -1; return }
  if (carouselIndex.value < 0) { carouselIndex.value = 0; startCarousel(); return }
  if (carouselIndex.value >= list.length) { carouselIndex.value = list.length - 1 }
})
onBeforeUnmount(() => { stopCarousel() })

/** CSS variables (unchanged) */
const heroVars = computed(() => {
  const isDefault = props.height === 530
  const h = props.compact && isDefault ? 400 : props.height
  const carousel = Math.max(160, Math.round(h * 0.6))
  const pt =
    h <= 320 ? '0.5rem' :
    h <= 380 ? '0.75rem' :
    h <= 440 ? '1.25rem' : '2.5rem'
  const waveBackH  = h <= 320 ? '40%' : h <= 400 ? '44%' : '54%'
  const waveFrontH = h <= 320 ? '46%' : h <= 400 ? '50%' : '60%'
  return {
    '--hero-min': `${h}px`,
    '--carousel-min': `${carousel}px`,
    '--hero-pt': pt,
    '--wave-back-h': waveBackH,
    '--wave-front-h': waveFrontH
  }
})
</script>

<template>
  <section
    class="amf-hero flex flex-col items-center justify-center bg-gradient-to-b from-[var(--color-amf-primary,#0056b3)] to-blue-800 pb-0 relative"
    :style="heroVars"
    @mouseenter="stopCarousel"
    @mouseleave="startCarousel"
  >
    <!-- Waves background (unchanged) -->
    <div class="hero-waves" aria-hidden="true">
      <div class="wave wave-back">
        <div class="wave-track">
          <svg class="wave-svg" viewBox="0 0 1200 200" preserveAspectRatio="none">
            <path d="M0,90 C150,130 350,60 600,90 C850,120 1050,70 1200,90 L1200,200 L0,200 Z" fill="rgba(255,255,255,0.08)"/>
          </svg>
          <svg class="wave-svg" viewBox="0 0 1200 200" preserveAspectRatio="none">
            <path d="M0,90 C150,130 350,60 600,90 C850,120 1050,70 1200,90 L1200,200 L0,200 Z" fill="rgba(255,255,255,0.08)"/>
          </svg>
        </div>
      </div>
      <div class="wave wave-mid">
        <div class="wave-track">
          <svg class="wave-svg" viewBox="0 0 1200 200" preserveAspectRatio="none">
            <path d="M0,100 C180,140 360,70 600,100 C840,130 1020,80 1200,100 L1200,200 L0,200 Z" fill="rgba(255,255,255,0.11)"/>
          </svg>
          <svg class="wave-svg" viewBox="0 0 1200 200" preserveAspectRatio="none">
            <path d="M0,100 C180,140 360,70 600,100 C840,130 1020,80 1200,100 L1200,200 L0,200 Z" fill="rgba(255,255,255,0.11)"/>
          </svg>
        </div>
      </div>
      <div class="wave wave-front">
        <div class="wave-track">
          <svg class="wave-svg" viewBox="0 0 1200 200" preserveAspectRatio="none">
            <path d="M0,110 C180,150 360,80 600,110 C840,140 1020,90 1200,110 L1200,200 L0,200 Z" fill="rgba(255,255,255,0.14)"/>
          </svg>
          <svg class="wave-svg" viewBox="0 0 1200 200" preserveAspectRatio="none">
            <path d="M0,110 C180,150 360,80 600,110 C840,140 1020,90 1200,110 L1200,200 L0,200 Z" fill="rgba(255,255,255,0.14)"/>
          </svg>
        </div>
      </div>
    </div>

    <div class="hero-caustics" aria-hidden="true"></div>

    <!-- HERO copy -->
    <div class="flex flex-col items-center z-10 max-w-3xl w-full mx-auto text-center pt-[var(--hero-pt)]">
      <h2 class="text-lg md:text-xl font-semibold mb-2 opacity-90 tracking-tight text-white">
        Formez-vous en ligne et en direct
      </h2>
      <h1 class="text-3xl md:text-5xl font-extrabold mb-3 tracking-tight text-white">
        AMF FORMATION & ÉVALUATION
      </h1>
      <p class="lead text-lg md:text-xl opacity-95 mb-7 text-white/90">
        Votre organisme de formation médicale continue agréé, 100% dédié aux professionnels de santé.
      </p>
    </div>

    <!-- Carousel -->
    <div
      v-if="!loading && upcomingCourses?.length"
      class="w-full max-w-3xl mx-auto mt-3 relative min-h-[var(--carousel-min)]"
    >
      <transition name="carousel-fade-slide" mode="out-in" appear>
        <div
          v-if="carouselVisible && currentCourse"
          :key="currentCourse.session_id ?? `i-${carouselIndex}`"
          class="absolute inset-0 w-full h-full"
          aria-live="polite"
        >
          <div class="relative w-full h-full">
            <button
              class="absolute top-3 right-3 text-lg text-gray-200/90 hover:text-white bg-white/10 backdrop-blur-sm rounded-full w-9 h-9 flex items-center justify-center border border-white/20 transition focus:outline-none focus:ring-2 focus:ring-white/50"
              @click.stop="closeCarousel"
              aria-label="Fermer"
              type="button"
            >
              ×
            </button>

            <CarouselCourseBox
              :course="currentCourse"
              :text-only="true"
              class="w-full h-full"
              @click="emit('click', currentCourse)"
              @next="advanceToNext"
              @prev="advanceToPrev"
              floating
            />
          </div>
        </div>
      </transition>

      <!-- Dots -->
      <div
        v-if="upcomingCourses.length > 1"
        class="relative z-10 mt-2 flex items-center justify-center gap-2"
        role="tablist"
        aria-label="Sélecteur de formation"
      >
        <button
          v-for="(c,i) in upcomingCourses"
          :key="c.session_id ?? i"
          class="h-2.5 w-2.5 rounded-full transition"
          :class="i===carouselIndex ? 'bg-white shadow-[0_0_0_2px_rgba(255,255,255,0.35)] scale-110' : 'bg-white/40 hover:bg-white/70'"
          :aria-selected="i===carouselIndex"
          role="tab"
          :aria-label="`Aller à la formation ${i+1}`"
          @click="goTo(i)"
        />
      </div>
    </div>
  </section>
</template>

<style scoped>
/* --- Hero base --- */
.amf-hero {
  position: relative;
  overflow: hidden;
  isolation: isolate;
  min-height: var(--hero-min, 530px);
}

/* --- Waves container --- */
.hero-waves {
  position: absolute;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

/* --- Wave bands (shared) --- */
.wave {
  position: absolute;
  left: -10%;
  width: 120%;
  height: 58%;
  bottom: -2%;
  -webkit-mask-image: linear-gradient(
    to top,
    rgba(0,0,0,0.95) 0%,
    rgba(0,0,0,0.7) 38%,
    rgba(0,0,0,0) 96%
  );
          mask-image: linear-gradient(
    to top,
    rgba(0,0,0,0.95) 0%,
    rgba(0,0,0,0.7) 38%,
    rgba(0,0,0,0) 96%
  );
  filter: blur(0.25px);
  will-change: transform;
}

/* Parallax layering + vertical bob */
.wave-back  { height: var(--wave-back-h, 54%); bottom: -5%; animation: wave-bob-back 14s ease-in-out -2s infinite; }
.wave-mid   { height: 56%;                      bottom: -3%; animation: wave-bob-mid  11s ease-in-out -1s infinite; }
.wave-front { height: var(--wave-front-h, 60%); bottom: -1%; animation: wave-bob-front  8s ease-in-out     infinite; }

/* Track slides horizontally to create motion */
.wave-track {
  position: absolute;
  width: 200%;
  height: 100%;
  animation: wave-slide 26s linear infinite;
  will-change: transform;
}
.wave-back  .wave-track { animation-duration: 42s; } /* slowest */
.wave-mid   .wave-track { animation-duration: 32s; }
.wave-front .wave-track { animation-duration: 22s; } /* fastest */

/* Two tiled SVGs for a seamless loop */
.wave-svg {
  position: absolute;
  top: 0;
  width: 50%;
  height: 100%;
}
.wave-svg:nth-child(1) { left: 0; }
.wave-svg:nth-child(2) { left: 50%; }

/* Horizontal travel */
@keyframes wave-slide {
  0%   { transform: translate3d(0,0,0); }
  100% { transform: translate3d(-50%,0,0); }
}

/* Vertical bob (amplitude varies per layer) */
@keyframes wave-bob-back  { 0%,100% { transform: translate3d(0,0,0) } 50% { transform: translate3d(0,-4px,0) } }
@keyframes wave-bob-mid   { 0%,100% { transform: translate3d(0,0,0) } 50% { transform: translate3d(0,-6px,0) } }
@keyframes wave-bob-front { 0%,100% { transform: translate3d(0,0,0) } 50% { transform: translate3d(0,-8px,0) } }

/* Optional: subtle caustics shimmer */
.hero-caustics {
  position: absolute;
  left: -5%;
  right: -5%;
  bottom: -10%;
  height: 65%;
  z-index: 0;
  pointer-events: none;
  mix-blend-mode: soft-light;
  opacity: 0.06; /* 0.04–0.08 looks good */
  background:
    radial-gradient(120% 80% at 50% 100%, rgba(255,255,255,0.25), transparent 60%),
    conic-gradient(from 0deg at 50% 100%,
      rgba(255,255,255,0.35) 0deg, transparent 90deg,
      rgba(255,255,255,0.35) 180deg, transparent 270deg,
      rgba(255,255,255,0.35) 360deg);
  animation: caustics-rotate 90s linear infinite;
  transform-origin: 50% 100%;
  will-change: transform;
}
@keyframes caustics-rotate {
  from { transform: rotate(0deg); }
  to   { transform: rotate(360deg); }
}

/* Respect reduced motion */
@media (prefers-reduced-motion: reduce) {
  .wave, .wave-track, .hero-caustics { animation: none !important; }
  .hero-caustics { opacity: 0; }
}

/* --- Carousel slide transition (unchanged) --- */
.carousel-fade-slide-enter-active,
.carousel-fade-slide-leave-active {
  transition: all 0.5s cubic-bezier(.43,1.05,.6,1);
  position: absolute !important;
  width: 100%;
  top: 0;
  left: 0;
}
.carousel-fade-slide-enter-from {
  opacity: 0;
  transform: translateX(60px) scale(0.97);
  z-index: 1;
}
.carousel-fade-slide-enter-to {
  opacity: 1;
  transform: translateX(0) scale(1);
}
.carousel-fade-slide-leave-from {
  opacity: 1;
  transform: translateX(0) scale(1);
  z-index: 2;
}
.carousel-fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-60px) scale(0.97);
}

.wave::after {
  content: "";
  position: absolute; inset: 0;
  background: linear-gradient(to top, transparent 55%, rgba(255,255,255,0.08) 100%);
  mix-blend-mode: screen;
  pointer-events: none;
  opacity: 0.6;
}

/* Tunables (per-hero) */
.amf-hero {
  --speed-back: 42s;
  --speed-mid: 30s;     /* a tad faster */
  --speed-front: 20s;   /* a tad faster */

  --bob-back: 4px;
  --bob-mid: 7px;       /* slightly bigger than back */
  --bob-front: 9px;     /* slightly bigger than mid */

  --opa-back: 0.10;
  --opa-mid:  0.13;
  --opa-front:0.16;
}

/* Use one bob keyframe + per-layer variables */
@keyframes wave-bob { 0%,100% { transform: translate3d(0,0,0) } 50% { transform: translate3d(0, var(--bob, -6px) * -1, 0) } }
.wave-back  { --bob: var(--bob-back);  animation: wave-bob 14s ease-in-out -2s infinite; bottom: -5%; }
.wave-mid   { --bob: var(--bob-mid);   animation: wave-bob 11s ease-in-out -1s infinite; bottom: -3%; }
.wave-front { --bob: var(--bob-front); animation: wave-bob  8s ease-in-out      infinite; bottom: -1%; }

/* Slide speeds + reverse mid for parallax richness */
.wave-track { animation: wave-slide var(--speed, 26s) linear infinite; }
.wave-back  .wave-track { --speed: var(--speed-back); }
.wave-mid   .wave-track { --speed: var(--speed-mid);  animation-direction: reverse; }
.wave-front .wave-track { --speed: var(--speed-front); }

/* Control fills via CSS (overrides SVG attributes) */
.wave-back  path  { fill: rgba(255,255,255,var(--opa-back)); }
.wave-mid   path  { fill: rgba(255,255,255,var(--opa-mid)); }
.wave-front path  { fill: rgba(255,255,255,var(--opa-front)); }

@media (max-width: 480px) {
  .hero-caustics { display: none; }
}
</style>

