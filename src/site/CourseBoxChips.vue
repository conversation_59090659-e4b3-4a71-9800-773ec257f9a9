<script setup>
import { formatStartDate } from './useCourseBox.js'

const props = defineProps({
  course: { type: Object, required: true }
})
</script>

<template>
  <div class="flex flex-wrap items-center gap-2 mb-1 text-xs md:text-sm justify-center w-full">
    <span class="inline-flex items-center px-2 py-0.5 rounded bg-[var(--color-amf-primary-light)] text-[var(--color-amf-primary)] font-semibold">
      <i class="fa fa-user-tie mr-1"></i> {{ course.organisme || 'AMF' }}
    </span>
    <span class="inline-flex items-center px-2 py-0.5 rounded bg-gray-100 text-gray-700 font-medium">
      <i
        v-if="course.is_online"
        class="fa fa-globe mr-1 text-[var(--color-amf-primary)]"
      ></i>
      <i
        v-else
        class="fa fa-location-dot mr-1 text-[var(--color-amf-primary)]"
      ></i>
      {{ course.is_online ? 'En ligne' : course.location_city || 'Lieu inconnu' }}
    </span>
    <span class="inline-flex items-center px-2 py-0.5 rounded bg-gray-50 text-cyan-700">
      <i class="fa-regular fa-calendar mr-1"></i>
      {{ formatStartDate(course.start_time) }}
    </span>
  </div>
</template>