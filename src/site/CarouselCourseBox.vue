<!-- CarouselCourseBox.vue -->
<script setup>
import { loadStageImage } from "../formation.js";
import { onMounted, ref } from "vue";
import router from "../router.js";

const props = defineProps({
  course: { type: Object, required: true },
  floating: { type: Boolean, default: false },
  /** When true, show only text + buttons (no background, no images) */
  textOnly: { type: Boolean, default: false }
})

const formatStartDate = (dt) => {
  if (!dt) return ''
  const d = new Date(dt)
  return d.toLocaleDateString('fr-FR', {
    weekday: 'long', day: '2-digit', month: 'long'
  }) + ' à ' + d.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })
}

const imageUrl = ref("/images/course_image.jpg")

onMounted(async () => {
  if (!props.textOnly) {
    imageUrl.value = props.course?.image
      ? props.course.image
      : await loadStageImage(props.course.image_url)
  }
})
</script>

<template>
  <!-- TEXT-ONLY (GLASS, wider + shorter, centered title, bottom row footer) -->
  <div v-if="textOnly" class="w-full">
    <article
      class="font-carousel mx-auto w-full max-w-3xl md:max-w-4xl
             rounded-2xl bg-white/10 border border-white/20 backdrop-blur-md
             px-4 pt-8 pb-3 sm:px-5 sm:pb-4 sm:pt-10
             shadow-[0_10px_30px_rgba(0,0,0,0.25)] text-white"
      aria-label="Mise en avant de formation"
    >
      <!-- Title -->
      <h3 class="text-center text-lg sm:text-xl md:text-2xl font-extrabold leading-snug tracking-tight drop-shadow-[0_1px_0_rgba(0,0,0,0.25)]">
        {{ course.libellestage || course.name }}
      </h3>

      <!-- Footer row: chips (left) + actions (right) -->
      <div class="mt-3 pt-2 border-t border-white/15
                  flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">

        <!-- Chips -->
        <div class="flex flex-wrap items-center gap-1.5 text-[12px]">
          <span class="inline-flex items-center gap-1 px-2 py-0.5 rounded-full bg-white/12 border border-white/20">
            <i class="fa fa-user-tie text-white/90"></i>
            <span class="text-white/90">{{ course.organisme || 'AMF' }}</span>
          </span>
          <span class="inline-flex items-center gap-1 px-2 py-0.5 rounded-full bg-white/12 border border-white/20">
            <i :class="course.is_online ? 'fa fa-globe' : 'fa fa-location-dot'" class="text-white/90"></i>
            <span class="text-white/90">{{ course.is_online ? 'En ligne' : course.location_city || 'Lieu inconnu' }}</span>
          </span>
          <span class="inline-flex items-center gap-1 px-2 py-0.5 rounded-full bg-white/12 border border-white/20">
            <i class="fa-regular fa-calendar text-white/90"></i>
            <span class="text-white/90">{{ formatStartDate(course.start_time) }}</span>
          </span>
        </div>

        <!-- Actions -->
        <div class="flex items-center gap-2 sm:justify-end">
          <button
            v-if="!course.registered"
            class="inline-flex items-center justify-center px-3.5 py-2 rounded-lg
                   bg-[var(--color-amf-primary)] hover:bg-[var(--color-amf-primary-dark)]
                   text-white text-sm font-semibold shadow
                   focus:outline-none focus:ring-2 focus:ring-white/50 transition"
            @click="router.push('/formations/' + course.session_id );"
          >
            S’inscrire
            <i class="fa fa-arrow-right ml-2 text-white/90"></i>
          </button>
          <span
            v-else
            class="inline-flex items-center px-3 py-2 rounded-lg bg-green-600 text-white text-sm font-semibold shadow"
          >
            <i class="fa fa-check mr-2"></i> Inscrit
          </span>

          <a
            href="#"
            class="inline-flex items-center gap-1 px-3 py-2 rounded-lg bg-white/0 hover:bg-white/10 border border-white/20
                   text-white/90 hover:text-white text-sm transition"
            @click="router.push('/formations/' + course.session_id );"
          >
            Détails
            <i class="fa fa-arrow-up-right-from-square text-white/80"></i>
          </a>
        </div>
      </div>
    </article>
  </div>

  <!-- IMAGE VARIANT (unchanged) -->
  <div
    v-else
    class="relative w-full max-w-3xl aspect-[3/2] mx-auto flex items-center justify-center group overflow-hidden"
    tabindex="0"
    role="button"
    aria-label="Voir la formation"
  >
    <!-- ...your existing image variant... -->
  </div>
</template>

<!-- Font just for the carousel card -->
<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@500;600;700&display=swap');

.font-carousel {
  font-family: "Inter", ui-sans-serif, system-ui, -apple-system, "Segoe UI",
               Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji",
               "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
</style>
