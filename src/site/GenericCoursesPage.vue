<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUpcomingCoursesStore } from './useUpcomingCoursesStore.js'
import GenericCourseBox from './GenericCourseBox.vue'
import AisyngFilterChips  from "../modules/filterChips/AisyngFilterChips.vue";
import SiteNavBar from './SiteNavBar.vue'
import SiteFooter from './SiteFooter.vue'

const props = defineProps({
  initialFilter: { type: String, default: '' }
})

const filter = ref(props.initialFilter)
const store = useUpcomingCoursesStore()

onMounted(async () => { await store.ensure() })

const loading = computed(() => store.status === 'loading' && !store.items.length)
const filteredCourses = computed(() => store.filtered(filter.value))

const gridCols = computed(() => {
  const n = filteredCourses.value.length
  return n < 6
    ? 'grid-cols-1 sm:grid-cols-2'
    : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
})

const chipOptions = [
  { label: 'Toutes', value: '' },
  { label: 'FAF-PM', value: 'FAF-PM' },
  { label: 'DPC', value: 'DPC' },
  { label: 'Autres', value: 'Autres' }
]
</script>

<template>
  <div>
    <SiteNavBar/>

    <section class="min-h-screen bg-[var(--color-amf-bg)] py-8">
      <div class="max-w-7xl mx-auto px-2">
        <h1 class="text-2xl md:text-3xl font-bold mb-6 text-center text-[var(--color-amf-primary)]">
          Toutes les formations à venir
        </h1>

        <!-- FILTER BUTTON BAR -->
        <AisyngFilterChips
          v-model="filter"
          :options="chipOptions"
          :multiple="false"
          aria-label="Filtres des formations"
        />

        <div v-if="loading" class="w-full flex flex-col items-center py-12 text-xl text-gray-500">
          <i class="fa fa-spinner fa-spin mb-2 text-2xl"></i>
          Chargement des cours…
        </div>

        <div v-else-if="filteredCourses.length === 0"
             class="w-full flex flex-col items-center py-16 text-lg text-gray-400">
          <i class="fa fa-calendar-times mb-2 text-3xl"></i>
          Aucune formation à venir pour ce filtre.
        </div>

        <div v-else class="grid gap-6 mt-8" :class="gridCols">
          <GenericCourseBox
            v-for="course in filteredCourses"
            :key="course.session_id"
            :course="course"
          />
        </div>
      </div>
    </section>

    <SiteFooter/>
  </div>
</template>
