// main.js
import { createApp } from 'vue'
import { createP<PERSON> } from 'pinia'
import App from './App.vue'
import router from './router'
import "./assets/amf.css";

import axios from 'axios'
import { VueFire, VueFireAuth } from 'vuefire'
import { firebaseApp } from './firebase'
import { getAuth, onAuthStateChanged, onIdTokenChanged } from 'firebase/auth'
import { stripDatesToLocalStrings } from './dateUtils.js'
import { SF_CULTURE, SYNCFUSION_KEY } from './config.js'
import { setCulture } from '@syncfusion/ej2-base'
import { ToastPlugin } from './modules/toast/toast.js'

// ---------- Axios cache busting (ok to keep) ----------
function isPlainObject(obj) {
  return Object.prototype.toString.call(obj) === '[object Object]'
}
axios.defaults.headers.get['Cache-Control'] = 'no-cache, no-store, must-revalidate'
axios.defaults.headers.get['Pragma'] = 'no-cache'
axios.defaults.headers.get['Expires'] = '0'

// ---------- NEW: wait for first auth state ----------
const auth = getAuth(firebaseApp)
// resolves after Firebase restores persisted session (user or null)
const authReady = new Promise((resolve) => {
  const off = onAuthStateChanged(auth, () => { off(); resolve() })
})

// Optional: mirror the latest ID token into localStorage (handy fallback)
onIdTokenChanged(auth, async (user) => {
  if (user) {
    const t = await user.getIdToken()
    try { localStorage.setItem('auth_token', t) } catch {}
  } else {
    try { localStorage.removeItem('auth_token') } catch {}
  }
})

// ---------- Request interceptor ----------
axios.interceptors.request.use(
  async (config) => {
    // ensure auth is initialized before we try to read currentUser/token
    await authReady

    // normalize payload before sending
    const data = config.data
    if (data && (isPlainObject(data) || Array.isArray(data))) {
      config.data = stripDatesToLocalStrings(data)
    }

    // attach token if available
    const user = auth.currentUser
    let token = null
    if (user) {
      token = await user.getIdToken()          // fresh token
    } else {
      try { token = localStorage.getItem('auth_token') } catch {} // fallback (may be null)
    }

    if (token) {
      config.headers = config.headers ?? {}
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// ---------- App boot ----------
const app = createApp(App)
app.use(router)
app.use(VueFire, { firebaseApp, modules: [VueFireAuth()] })
app.use(createPinia())
app.use(ToastPlugin)

setCulture(SF_CULTURE)
app.mount('#app')
