import {L10n, loadCldr} from '@syncfusion/ej2-base';
import frNumberData from '@syncfusion/ej2-cldr-data/main/fr/numbers.json';
import frtimeZoneData from '@syncfusion/ej2-cldr-data/main/fr/timeZoneNames.json';
import frGregorian from '@syncfusion/ej2-cldr-data/main/fr/ca-gregorian.json';
import frNumberingSystem from '@syncfusion/ej2-cldr-data/supplemental/numberingSystems.json';

const localeText = {
  "fr": {
    "grid":{
      "Add": "Ajouter",
      "Edit": "Modifier",
      "Delete": "Supprimer",
      "EmptyRecord": "Aucun enregistrement à afficher",
      "Save": "Enregistrer",
      "SaveButton": "Enregistrer",
      "Cancel": "Annuler",
      "CancelButton": "Annuler"
    }
  }
}

L10n.load(localeText);
loadCldr(frNumberData, frtimeZoneData, frGregorian, frNumberingSystem);


export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const SYNCFUSION_KEY = "Ngo9BigBOggjHTQxAR8/V1NNaF5cXmBCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWXxcd3RcR2NfV0VxXUtWYUA="

export const LOGO_NAME="logo_amf_web.png"
//export const LOGO_NAME="logo_aisyng_2.png"

export const SF_CULTURE = "fr"

export const firebaseConfig = {
  apiKey: "AIzaSyCLNTmVcLX51G6SRtQ6Tu44Yby7t1_SvZo",
  authDomain: "amf-formation.firebaseapp.com",
  projectId: "amf-formation",
  storageBucket: "amf-formation.firebasestorage.app",
  messagingSenderId: "541484868075",
  appId: "1:541484868075:web:b583ca35f6b7b95852f9ae"
};

export const clientId="AMF"

export const DATE_FORMAT="dd MMM yyyy"
export const DATE_TIME_FORMAT="dd MMM yyyy HH:mm"