<template>
  <button
    v-bind="$attrs"
    :type="nativeType"
    :class="klass"
    :aria-busy="loading || undefined"
    :disabled="loading || undefined"
    @click="$emit('click', $event)"
  >
    <!-- optional icon slots -->
    <slot name="icon-left" />
    <slot />
    <slot name="icon-right" />

    <!-- inline spinner -->
    <svg v-if="loading" class="animate-spin ml-2 h-4 w-4" viewBox="0 0 24 24" aria-hidden="true">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
    </svg>
  </button>
</template>

<script setup lang="ts">
import { computed, useAttrs } from 'vue'

export type OldVariant = 'normal' | 'primary' | 'warning' | 'danger' | 'success' | 'ghost' | 'primaryBlue' | 'secondary' | 'purple' | 'indigo'
export type NewVariant = 'solid' | 'outline' | 'ghost'
export type Variant = OldVariant | NewVariant
export type Size = 'sm' | 'md' | 'lg'

const props = withDefaults(defineProps<{
  variant?: Variant
  size?: Size
  loading?: boolean
  block?: boolean
  rounded?: 'md' | 'lg' | 'xl' | 'full'
  iconOnly?: boolean
  extraClass?: string
}>(), {
  variant: 'outline',
  size: 'md',
  loading: false,
  block: false,
  rounded: 'md',
  iconOnly: false,
})

const attrs = useAttrs()
const nativeType = computed(() => (attrs.type as string) ?? 'button')

/** Base + interaction (focus ring tuned to teal) */
const base = [
  'inline-flex items-center justify-center gap-2 font-medium',
  'transition-all duration-150',
  'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-1 focus-visible:ring-teal-300',
  'disabled:opacity-50 disabled:pointer-events-none active:scale-95',
].join(' ')

const sizeClasses: Record<Size, string> = {
  sm: 'px-2.5 py-1.5 text-sm',
  md: 'px-3.5 py-2 text-sm',
  lg: 'px-4.5 py-2.5 text-base',
}

const iconOnlySizes: Record<Size, string> = {
  sm: 'w-8 h-8 p-0',
  md: 'w-9 h-9 p-0',
  lg: 'w-10 h-10 p-0',
}

const roundedMap = {
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
  full: 'rounded-full',
} as const

/**
 * Teal-first variants (using your theme vars with fallbacks):
 * --amf-primary:      #008891
 * --amf-primary-dark: #005f73
 */
const T_PRIMARY = 'var(--amf-primary, #008891)'
const T_PRIMARY_DARK = 'var(--amf-primary-dark, #005f73)'

const variants: Record<Variant, string> = {
  // NEW (teal)
  solid:
    'bg-[var(--amf-primary,_#008891)] text-white ' +
    'border border-[var(--amf-primary,_#008891)] ' +
    'hover:bg-[var(--amf-primary-dark,_#005f73)]',

  outline:
    'bg-white text-[var(--amf-primary,_#008891)] ' +
    'border border-[var(--amf-primary,_#008891)] ' +
    'hover:bg-teal-50',

  ghost:
    'bg-teal-50 text-[var(--amf-primary-dark,_#005f73)] ' +
    'border border-transparent hover:bg-teal-100',

  // OLD aliases → teal equivalents
  normal:
    'bg-white text-[var(--amf-primary,_#008891)] ' +
    'border border-[var(--amf-primary,_#008891)] ' +
    'hover:bg-teal-50',

  primary:
    'bg-[var(--amf-primary,_#008891)] text-white ' +
    'border border-[var(--amf-primary,_#008891)] ' +
    'hover:bg-[var(--amf-primary-dark,_#005f73)]',

  primaryBlue:
    'bg-blue-600 text-white ' +
    'border border-blue-600 ' +
    'hover:bg-blue-700',

  secondary:
    'bg-gray-100 text-gray-900 ' +
    'border border-gray-300 ' +
    'hover:bg-gray-200',

  purple:
    'bg-purple-600 text-white ' +
    'border border-purple-600 ' +
    'hover:bg-purple-700',

  indigo:
    'bg-indigo-600 text-white ' +
    'border border-indigo-600 ' +
    'hover:bg-indigo-700',

  // Semantic intents
  warning: 'bg-amber-100 text-amber-900 border border-amber-200 hover:bg-amber-200',
  danger:  'bg-red-100 text-red-900 border border-red-200 hover:bg-red-200',
  success: 'bg-green-100 text-green-900 border border-green-200 hover:bg-green-200',
}

const klass = computed(() => [
  base,
  props.iconOnly ? iconOnlySizes[props.size] : sizeClasses[props.size],
  roundedMap[props.rounded],
  variants[props.variant],
  props.block && !props.iconOnly ? 'w-full justify-center' : '',
  props.extraClass,
])
</script>

<style scoped>
/* utility-only */
</style>
