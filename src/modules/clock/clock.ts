// @aisyng/clock
// Production-ready, server-synced clock with Paris-time formatting & helpers.
// - Syncs client clock to a trusted backend endpoint (NTP-synced host)
// - Computes drift (offset) and applies it to Date.now()
// - Formats with Europe/Paris regardless of user's OS TZ
// - Optional fallback to worldtimeapi.org (disabled by default)
// - Includes a small Vue 3 composable

// -----------------------------------------
// index.ts (library entry)
// -----------------------------------------

import { getTime } from '../../services';

export type ClockOptions = {
  /** IANA TZ for display; default "Europe/Paris" */
  timezone?: string;
  /** Auto-resync every N ms (set 0/undefined to disable). Recommended 30–60min */
  resyncIntervalMs?: number;
  /** Use a public fallback only if your endpoint fails. */
  fallback?: {
    enabled?: boolean; // default false
    /** Optional override; by default uses ETC/UTC from worldtimeapi */
    url?: string;
  };
  /** Called after any sync attempt (success or failure) */
  onSync?: (info: SyncInfo) => void;
};

export type SyncInfo = {
  ok: boolean;
  offsetMs: number;        // current serverOffsetMs
  rttMs?: number;          // measured roundtrip time
  halfRttMs?: number;      // half RTT used in calc
  serverEpochMs?: number;  // UTC ms reported by server
  at: number;              // client Date.now() when sync completed
  source: 'backend' | 'fallback' | 'none';
  error?: unknown;
};

export class ServerClock {
  private timezone: string;
  private resyncIntervalMs?: number;
  private fallbackUrl?: string;
  private fallbackEnabled: boolean;
  private onSync?: (info: SyncInfo) => void;

  private serverOffsetMs = 0; // add to Date.now() to get server-true UTC
  private lastSyncInfo: SyncInfo = { ok: false, offsetMs: 0, at: Date.now(), source: 'none' };
  private intervalId: number | undefined;
  private visibilityHandler: (() => void) | undefined;

  constructor(opts: ClockOptions) {
    this.timezone = opts.timezone ?? 'Europe/Paris';
    this.resyncIntervalMs = opts.resyncIntervalMs;
    this.fallbackEnabled = !!opts.fallback?.enabled;
    this.fallbackUrl = opts.fallback?.url;
    this.onSync = opts.onSync;
  }

  /** Current trusted instant (as Date) using server drift compensation. */
  now(): Date {
    return new Date(Date.now() + this.serverOffsetMs);
    // The Date object always holds an absolute instant; formatting decides the zone.
  }

  /** Format a given Date in the configured timezone (default Paris). */
  formatInTz(d: Date, options?: Intl.DateTimeFormatOptions): string {
    return new Intl.DateTimeFormat(undefined, { timeZone: this.timezone, ...options }).format(d);
  }

  /** Format convenience: HH:mm in Paris */
  formatParisHHmm(d: Date): string {
    return new Intl.DateTimeFormat('fr-FR', { timeZone: this.timezone, hour: '2-digit', minute: '2-digit' }).format(d);
  }

  parseAsUtc(input: string): number {
  // Try to detect a GMT offset like "GMT+0100"
  if (/GMT[+-]\d{4}/.test(input)) {
    // Drop the GMT offset and timezone name
    const cleaned = input.replace(/GMT[+-]\d{4}.*$/, "UTC");
    const d = new Date(cleaned);
    return d.getTime();
  }

  // Otherwise assume ISO with +HH:mm
  const noTz = input.replace(/([+-]\d{2}:\d{2})$/, "Z");
  return Date.parse(noTz);
}

  /** Expose current offset in milliseconds. */
  getOffsetMs(): number { return this.serverOffsetMs; }
  getLastSync(): SyncInfo { return this.lastSyncInfo; }

  /** Perform a single sync against backend, with optional fallback. */
  async syncOnce(): Promise<SyncInfo> {
    const t0 = performance.now();
    try {
      const data = await getTime()
      const t1 = performance.now();
      console.log("CLOCK:", data);
      // Expected backend payload (example): { epoch_ms: number }
      // const serverEpochMs = typeof data.epoch_ms === 'number' ? data.epoch_ms : Date.parse(data.utc_iso);
      const serverEpochMs = this.parseAsUtc(data.paris_iso);
      const rttMs = t1 - t0;
      const halfRttMs = rttMs / 2;

      const now_ms = new Date();
      const now_ms_adjusted = this.parseAsUtc(now_ms.toString());
      const serverEpochMsAtArrival = serverEpochMs + halfRttMs;
      this.serverOffsetMs = serverEpochMsAtArrival - now_ms_adjusted;

      const info: SyncInfo = {
        ok: true,
        offsetMs: this.serverOffsetMs,
        rttMs,
        halfRttMs,
        serverEpochMs,
        at: Date.now(),
        source: 'backend',
      };
      this.lastSyncInfo = info;
      this.onSync?.(info);
      return info;
    } catch (error) {
      if (this.fallbackEnabled) {
        const fb = await this.syncViaFallback(error);
        this.lastSyncInfo = fb;
        this.onSync?.(fb);
        return fb;
      }
      const info: SyncInfo = { ok: false, offsetMs: this.serverOffsetMs, at: Date.now(), source: 'none', error };
      this.lastSyncInfo = info;
      this.onSync?.(info);
      return info;
    }
  }

  private async syncViaFallback(originalError?: unknown): Promise<SyncInfo> {
    const t0 = performance.now();
    try {
      const url = this.fallbackUrl ?? 'https://worldtimeapi.org/api/timezone/Etc/UTC';
      const res = await fetch(url, { cache: 'no-store' });
      const t1 = performance.now();
      if (!res.ok) throw new Error(`Fallback HTTP ${res.status}`);
      const data = await res.json();
      const utcIso = data.utc_datetime ?? data.datetime ?? data.utcDateTime ?? data.dateTime;
      const serverEpochMs = Date.parse(utcIso);
      const rttMs = t1 - t0;
      const halfRttMs = rttMs / 2;
      const serverEpochMsAtArrival = serverEpochMs + halfRttMs;
      this.serverOffsetMs = serverEpochMsAtArrival - Date.now();

      return { ok: true, offsetMs: this.serverOffsetMs, rttMs, halfRttMs, serverEpochMs, at: Date.now(), source: 'fallback' };
    } catch (error) {
      return { ok: false, offsetMs: this.serverOffsetMs, at: Date.now(), source: 'fallback', error: originalError ?? error };
    }
  }

  /** Start auto resync + resync on visibility regain. */
  startAutoSync(): void {
    if (this.resyncIntervalMs && !this.intervalId) {
      this.intervalId = window.setInterval(() => this.syncOnce(), this.resyncIntervalMs);
    }
    if (!this.visibilityHandler) {
      this.visibilityHandler = () => { if (document.visibilityState === 'visible') this.syncOnce(); };
      document.addEventListener('visibilitychange', this.visibilityHandler);
    }
  }

  /** Stop auto resync + listeners. */
  stopAutoSync(): void {
    if (this.intervalId) { clearInterval(this.intervalId); this.intervalId = undefined; }
    if (this.visibilityHandler) { document.removeEventListener('visibilitychange', this.visibilityHandler); this.visibilityHandler = undefined; }
  }
}

// -----------------------------------------
// Paris helpers (treat naive server strings as Paris wall time)
// -----------------------------------------

const HAS_TZ_RE = /[Zz]|[+-]\d{2}:\d{2}$/;
//export const PARIS_TZ = 'Europe/Paris';

const fmtParisYMD = new Intl.DateTimeFormat("fr-FR", {
  year: "numeric", month: "2-digit", day: "2-digit",
});

const fmtParisFull = new Intl.DateTimeFormat("fr-FR", {
  year: "numeric", month: "2-digit", day: "2-digit", hour: "2-digit", minute: "2-digit"
});

const fmtParisShortDate = new Intl.DateTimeFormat("fr-FR", {
  weekday: "short", day: "2-digit", month: "short",
});
const fmtParisHHmm = new Intl.DateTimeFormat("fr-FR", {
  hour: "2-digit", minute: "2-digit",
});

/**
 * Interpret a naive ISO-like string as Paris wall time (DST-aware),
 * or pass through any string that already has an explicit TZ/offset.
 */
export function parseAsParis(dateLike?: string | Date | null): Date | null {
  return parseAsItIs(dateLike);
  if (!dateLike) return null;
  if (dateLike instanceof Date) return dateLike;
  const s = String(dateLike).trim();
  if (HAS_TZ_RE.test(s)) return new Date(s);

  const m = s.match(/^(\d{4})-(\d{2})-(\d{2})(?:[ T](\d{2}):(\d{2})(?::(\d{2}))?)?$/);
  if (!m) return new Date(s);
  const [_, y, mo, d, hh = '00', mi = '00', ss = '00'] = m;

  // Step 1: assume the naive parts are UTC, then see what Paris wall time that shows
  const u0 = Date.UTC(+y, +mo - 1, +d, +hh, +mi, +ss);
  const asParis = new Intl.DateTimeFormat('en-GB', {
    hour12: false,
    year: 'numeric', month: '2-digit', day: '2-digit',
    hour: '2-digit', minute: '2-digit', second: '2-digit',
  }).formatToParts(new Date(u0));
  const parts = Object.fromEntries(asParis.map(p => [p.type, p.value]));
  const uShown = Date.UTC(+parts.year, +parts.month - 1, +parts.day, +parts.hour, +parts.minute, +parts.second);

  // Offset produced (Paris wall time vs naive UTC). Subtract to land on correct instant.
  const tzShiftMs = uShown - u0; // e.g. +7200000 during CEST
  return new Date(u0 - tzShiftMs);
}


/**
 * Parse a string or Date as *naive local wall time*, without TZ adjustment.
 * Example: "2025-01-01 13:00" → Date corresponding to 2025-01-01 13:00,
 * exactly as written (not shifted by Paris offset).
 *
 * ⚠️ Note: JavaScript Date always stores an absolute UTC instant.
 * This function encodes the naive fields directly as if they were UTC.
 * Displaying them in Paris time will still show 13:00, but in UTC you’ll
 * see 12:00 or 14:00 depending on DST. That’s expected.
 */
export function parseAsItIs(dateLike?: string | Date | null): Date | null {
  if (dateLike === null) return null;
  return new Date(dateLike);
  if (!dateLike) return null;
  if (dateLike instanceof Date) return dateLike;

  const s = String(dateLike).trim();
  if (HAS_TZ_RE.test(s)) return new Date(s);

  const m =
    s.match(
      /^(\d{4})-(\d{2})-(\d{2})(?:[ T](\d{2}):(\d{2})(?::(\d{2}))?)?$/
    ) || null;
  if (!m) return new Date(s);

  const [, y, mo, d, hh = "00", mi = "00", ss = "00"] = m;

  // Direct UTC encode of the naive parts.
  const utcMs = Date.UTC(+y, +mo - 1, +d, +hh, +mi, +ss);

  return new Date(utcMs);
}

export function getTodayInParis(): Date {
  const parts = new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric', month: '2-digit', day: '2-digit',
  }).formatToParts(new Date());
  const o = Object.fromEntries(parts.map(p => [p.type, p.value]));
  return parseAsParis(`${o.year}-${o.month}-${o.day}T00:00:00`)!;
}

export function isSameDayParis(a?: Date | null, b?: Date | null): boolean {
  if (!a || !b) return false;
  const fmt = new Intl.DateTimeFormat('fr-FR', { year: 'numeric', month: '2-digit', day: '2-digit' });
  return fmt.format(a) === fmt.format(b);
}

export function isTodayParis(dateLike?: string | Date | null): boolean {
  if (!dateLike) return false;
  const d = dateLike instanceof Date ? dateLike : parseAsParis(dateLike);
  return !!d && isSameDayParis(d, getTodayInParis());
}

export function formatDateShort(dateLike?: string | null): string {
  if (!dateLike) return "";
  const d = parseAsParis(dateLike)!;
  return fmtParisShortDate.format(d);
}

export function formatDateFull(dateLike?: Date | string | null): string {
  if (!dateLike) return "";
  const d = parseAsParis(dateLike)!;
  return fmtParisFull.format(d);
}

export function formatTimeHHMM(dateLike?: string | null): string {
  if (!dateLike) return "";
  const d = parseAsParis(dateLike)!;
  return fmtParisHHmm.format(d);
}

export function formatDateRange(start?: string | null, end?: string | null): string {
  if (!start && !end) return "";
  const s = start ? parseAsParis(start)! : null;
  const e = end ? parseAsParis(end)! : null;
  if (s && e && isSameDayParis(s, e)) {
    return `${formatDateShort(start!)} • ${formatTimeHHMM(start!)}–${formatTimeHHMM(end!)}`;
  }
  const left  = s ? `${formatDateShort(start!)} ${formatTimeHHMM(start!)}` : "";
  const right = e ? `${formatDateShort(end!)} ${formatTimeHHMM(end!)}` : "";
  return `${left} → ${right}`.trim();
}

// -----------------------------------------
// useServerClock.ts (Vue 3 composable)
// -----------------------------------------

import { ref, computed, onMounted, onBeforeUnmount } from 'vue';

export function useServerClock(clock: ServerClock, opts?: { tickMs?: number }) {
  const tickMs = opts?.tickMs ?? 1000;
  const now = ref<Date>(clock.now());
  const isSynced = ref<boolean>(clock.getLastSync().ok);
  const lastSync = ref<SyncInfo>(clock.getLastSync());
  let timer: number | undefined;

  async function sync() {
    const info = await clock.syncOnce();
    isSynced.value = info.ok;
    lastSync.value = info;
    now.value = clock.now();
  }

  function start() {
    if (!timer) timer = window.setInterval(() => { now.value = clock.now(); }, tickMs);
    clock.startAutoSync();
  }

  function stop() {
    if (timer) { clearInterval(timer); timer = undefined; }
    clock.stopAutoSync();
  }

  onMounted(async () => { await sync(); start(); });
  onBeforeUnmount(() => stop());

  const parisHHmm = computed(() => clock.formatParisHHmm(now.value));
  const localHHmm = computed(() => new Intl.DateTimeFormat(undefined, { hour: '2-digit', minute: '2-digit' }).format(now.value));

  return { now, parisHHmm, localHHmm, isSynced, lastSync, sync, start, stop };
}
