<!-- ──────────────────────────────────────────────────────────────────────────
File: src/components/ToastHost.vue
Renders stacks for each position + transitions. Uses CSS variables for theme.
────────────────────────────────────────────────────────────────────────── -->

<template>
  <div class="toast-portal" aria-live="polite" aria-atomic="true">
    <div
      v-for="pos in positions"
      :key="pos"
      class="toast-stack"
      :class="`pos-${pos}`"
    >
      <TransitionGroup
        name="toast-slide-fade"
        tag="div"
        @after-leave="onAfterLeave"
      >
        <ToastItem
          v-for="t in toastsByPosition[pos]"
          :key="t.id"
          :toast="t"
          @request-close="emit('close', t.id)"
        />
      </TransitionGroup>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import ToastItem from './ToastItem.vue'

const props = defineProps({
  toasts: { type: Array, required: true },
})

const emit = defineEmits(['close', 'after-leave'])

const positions = [
  'top-left',
  'top-center',
  'top-right',
  'center',
  'bottom-left',
  'bottom-center',
  'bottom-right',
]

// Build a single computed map so each v-for sees a plain array (not a new computed)
const toastsByPosition = computed(() => {
  const map = Object.fromEntries(positions.map(p => [p, []]))
  for (const t of props.toasts) {
    if (!t || !t.visible) continue
    const p = positions.includes(t.position) ? t.position : 'bottom-center'
    map[p].push(t)
  }
  return map
})

function onAfterLeave(el) {
  // Requires ToastItem root to have :data-toast-id="toast.id"
  const id = Number(el?.dataset?.toastId)
  if (!Number.isNaN(id)) emit('after-leave', id)
}
</script>

<style scoped>
.toast-portal {
  position: fixed;
  inset: 0;
  pointer-events: none; /* children re-enable */
  z-index: 1000000;
}
.toast-stack {
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  width: 100%;
  pointer-events: none;
  padding: 12px;
}

/* Positions */
.pos-top-left     { top: 12px; left: 0; align-items: flex-start; }
.pos-top-center   { top: 12px; left: 0; }
.pos-top-right    { top: 12px; right: 0; align-items: flex-end; }
.pos-bottom-left  { bottom: 12px; left: 0; align-items: flex-start; }
.pos-bottom-center{ bottom: 12px; left: 0; }
.pos-bottom-right { bottom: 12px; right: 0; align-items: flex-end; }
.pos-center       { top: 50%; left: 0; transform: translateY(-50%); }

/* Safe area padding for phones (e.g., iOS home indicator) */
.pos-bottom-left,
.pos-bottom-center,
.pos-bottom-right {
  padding-bottom: calc(16px + env(safe-area-inset-bottom, 0));
}
.pos-top-left,
.pos-top-center,
.pos-top-right {
  padding-top: calc(16px + env(safe-area-inset-top, 0));
}

/* Animations */
.toast-slide-fade-enter-active,
.toast-slide-fade-leave-active { transition: all .25s ease, opacity .25s ease; }

.pos-top-left    .toast-slide-fade-enter-from,
.pos-bottom-left .toast-slide-fade-enter-from { transform: translateX(-6px); opacity: 0.001; }

.pos-top-right    .toast-slide-fade-enter-from,
.pos-bottom-right .toast-slide-fade-enter-from { transform: translateX(6px); opacity: 0.001; }

.pos-top-center .toast-slide-fade-enter-from,
.pos-bottom-center .toast-slide-fade-enter-from,
.pos-center .toast-slide-fade-enter-from { transform: translateY(6px); opacity: 0.001; }

.toast-slide-fade-leave-to { opacity: 0.001; transform: translateY(6px); }

/* Small screens: allow near-full width and a bit more spacing between stacked toasts */
@media (max-width: 640px) {
  .toast-stack { gap: 14px; padding: 14px; }
}
</style>
