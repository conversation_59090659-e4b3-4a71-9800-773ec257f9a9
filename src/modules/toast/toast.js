// ──────────────────────────────────────────────────────────────────────────────
// File: src/plugins/toast.js
// A tiny, reusable Toast plugin for Vue 3 (no CSS framework required).
// - Positions: 'bottom-center' (default), 'top-center', 'top-right', 'top-left',
//              'bottom-right', 'bottom-left', 'center'
// - Types: 'success' | 'error' | 'info' | 'warning'
// - Features: timeout + fade-out, pause on hover, action button, dismiss button,
//             programmatic API + composable, accessible roles.
// - Styling via CSS variables; easy to theme to match your app.
// ──────────────────────────────────────────────────────────────────────────────

import { reactive, readonly, createVNode, render, inject } from 'vue'
import ToastHost from './AisyngToastHost.vue'

const ToastSymbol = Symbol('toast')

// Internal reactive state
const state = reactive({ toasts: [] })
let seed = 0

/**
 * @typedef {Object} ToastAction
 * @property {string} label
 * @property {(toast: Toast)=>void} [onClick]
 * @property {string} [href] // if provided, will render an <a>
 *
 * @typedef {Object} Toast
 * @property {number} id
 * @property {'success'|'error'|'info'|'warning'} type
 * @property {string} [title]
 * @property {string} message
 * @property {number} duration // ms (0 = sticky)
 * @property {'bottom-center'|'top-center'|'top-right'|'top-left'|'bottom-right'|'bottom-left'|'center'} position
 * @property {boolean} dismissible
 * @property {ToastAction} [action]
 * @property {boolean} visible
 * @property {number} createdAt
 */

const defaults = {
  type: 'info',
  duration: 5000,
  position: 'bottom-center',
  dismissible: true,
}

function show(opts) {
  const id = ++seed
  const toast = {
    id,
    ...defaults,
    ...opts,
    visible: true,
    createdAt: Date.now(),
  }
  state.toasts.push(toast)
  return id
}

function success(message, opts = {}) {
  return show({ type: 'success', message, ...opts })
}
function error(message, opts = {}) {
  return show({ type: 'error', message, ...opts })
}
function info(message, opts = {}) {
  return show({ type: 'info', message, ...opts })
}
function warning(message, opts = {}) {
  return show({ type: 'warning', message, ...opts })
}

function close(id) {
  const t = state.toasts.find(t => t.id === id)
  if (t) t.visible = false
}
function remove(id) {
  const idx = state.toasts.findIndex(t => t.id === id)
  if (idx !== -1) state.toasts.splice(idx, 1)
}
function clear() {
  state.toasts.forEach(t => (t.visible = false))
}

// Provide a composable for components
export function useToast() {
  const api = inject(ToastSymbol)
  if (!api) throw new Error('ToastPlugin not installed')
  return api
}

// Optional: module-level reference if you want to toast from non-setup modules
export let toast = /** @type {ReturnType<typeof createApi>|null} */ (null)

function createApi() {
  return {
    // read-only array so consumers can’t mutate directly
    toasts: readonly(state.toasts),
    show,
    success,
    error,
    info,
    warning,
    close,
    remove,
    clear,
  }
}

export const ToastPlugin = {
  /** @param {import('vue').App} app */
  install(app) {
    const api = createApi()
    toast = api // set module-level handle (optional)

    // Mount a host into <body>
    if (typeof document !== 'undefined') {
      const hostEl = document.createElement('div')
      hostEl.id = 'amf-toast-host'
      document.body.appendChild(hostEl)
      const vnode = createVNode(ToastHost, {
        toasts: state.toasts,
        onClose: (id) => api.close(id),
        onAfterLeave: (id) => api.remove(id),
      })
      render(vnode, hostEl)
    }

    // provide & global property
    app.provide(ToastSymbol, api)
    app.config.globalProperties.$toast = api
  },
}

