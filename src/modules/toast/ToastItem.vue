<!-- ────────────────────────────────────────────────────────────────────────── -->
<!-- File: src/components/ToastItem.vue                                       -->
<!-- Single toast UI with icons, action, close, progress bar, pause-on-hover. -->
<!-- ────────────────────────────────────────────────────────────────────────── -->

<template>
  <div
    class="toast"
    :class="[`is-${toast.type}`]"
    role="status"
    :aria-live="toast.type === 'error' ? 'assertive' : 'polite'"
    :data-toast-id="toast.id"
    @mouseenter="pause"
    @mouseleave="resume"
  >
    <div class="toast__icon" aria-hidden="true" v-html="icon" />
    <div class="toast__content">
      <div v-if="toast.title" class="toast__title">{{ toast.title }}</div>
      <div class="toast__message">{{ toast.message }}</div>
      <div v-if="toast.action" class="toast__actions">
        <a v-if="toast.action.href" :href="toast.action.href" class="toast__btn">{{ toast.action.label }}</a>
        <button v-else class="toast__btn" @click="onAction">{{ toast.action.label }}</button>
      </div>
      <div v-if="toast.duration > 0" class="toast__progress" :style="{ '--toast-duration': toast.duration + 'ms' }" />
    </div>

    <button
      v-if="toast.dismissible"
      class="toast__close"
      aria-label="Close notification"
      @click="$emit('request-close')"
    >
      ×
    </button>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, computed } from 'vue'

const props = defineProps({ toast: { type: Object, required: true } })
const emit = defineEmits(['request-close', 'after-leave'])

let timer = null
const startedAt = ref(Date.now())
const remaining = ref(props.toast.duration)
const hovered = ref(false)

function startTimer() {
  if (!props.toast.duration) return
  clearTimeout(timer)
  timer = setTimeout(() => emit('request-close'), remaining.value)
}
function pause() {
  if (!props.toast.duration || hovered.value) return
  hovered.value = true
  remaining.value = Math.max(0, remaining.value - (Date.now() - startedAt.value))
  clearTimeout(timer)
}
function resume() {
  if (!props.toast.duration) return
  if (!hovered.value) return
  hovered.value = false
  startedAt.value = Date.now()
  startTimer()
}

onMounted(() => {
  startedAt.value = Date.now()
  startTimer()
})

onBeforeUnmount(() => clearTimeout(timer))

const icon = computed(() => {
  switch (props.toast.type) {
    case 'success': return `
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="10" cy="10" r="10" class="i-bg"/>
        <path d="M6 10.5l2.5 2.5L14 7.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>`
    case 'error': return `
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="10" cy="10" r="10" class="i-bg"/>
        <path d="M7 7l6 6M13 7l-6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>`
    case 'warning': return `
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="10" cy="10" r="10" class="i-bg"/>
        <path d="M10 5v7M10 15v0" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>`
    default: return `
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="10" cy="10" r="10" class="i-bg"/>
        <path d="M10 5a5 5 0 100 10 5 5 0 000-10z" fill="currentColor"/>
      </svg>`
  }
})

function onAction() {
  if (props.toast.action && typeof props.toast.action.onClick === 'function') {
    props.toast.action.onClick(props.toast)
  }
}
</script>

<style scoped>
/* Local defaults – no :root needed */
.toast {
  /* Layout + sizing */
  --toast-max-width: 560px;
  --toast-radius: 16px;

  /* Base surfaces */
  --toast-bg: rgba(17, 24, 39, 0.78);
  --toast-fg: #fff;
  --toast-border: rgba(255,255,255,0.18);

  /* Shadow */
  --toast-shadow:
    0 14px 34px rgba(0,0,0,.28),
    0 6px 14px rgba(0,0,0,.18);

  /* Accent palette */
  --toast-accent-success: #10b981;
  --toast-accent-error:   #ef4444;
  --toast-accent-warning: #f59e0b;
  --toast-accent-info:    #3b82f6;

  /* Default accent (overridden by .is-*) */
  --toast-accent: var(--toast-accent-info);
}

/* Per-type overrides */
.is-success { --toast-accent: var(--toast-accent-success); }
.is-error   { --toast-accent: var(--toast-accent-error); }
.is-warning { --toast-accent: var(--toast-accent-warning); }
.is-info    { --toast-accent: var(--toast-accent-info); }

/* Core card */
.toast {
  pointer-events: auto;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 12px;
  align-items: start;

  max-width: var(--toast-max-width);
  width: min(100% - 24px, var(--toast-max-width));
  color: var(--toast-fg);
  background: var(--toast-bg);
  border: 1px solid var(--toast-border);
  border-radius: var(--toast-radius);
  box-shadow: var(--toast-shadow);
  backdrop-filter: saturate(170%) blur(10px);
  -webkit-backdrop-filter: saturate(170%) blur(10px);
  padding: 14px 14px 12px 14px;
  position: relative;
  overflow: hidden;
  word-break: break-word;
}

/* Subtle accent wash when supported (keeps base background as fallback) */
@supports (color: color-mix(in oklab, red, white)) {
  .toast {
    background:
      linear-gradient(
        0deg,
        color-mix(in oklab, var(--toast-accent) 10%, transparent),
        color-mix(in oklab, var(--toast-accent) 10%, transparent)
      ),
      var(--toast-bg);
    border-color: color-mix(in oklab, var(--toast-accent) 20%, var(--toast-border));
  }
}

/* Left accent bar – wider and no transform so it’s clearly visible */
.toast::before {
  content: "";
  position: absolute;
  inset: 0 auto 0 0;
  width: 6px;                 /* was 3px */
  background: var(--toast-accent);
  opacity: .95;
}

/* Icon + progress in accent color */
.toast__icon { margin-top: 2px; color: var(--toast-accent); }
.toast__icon .i-bg { fill: currentColor; opacity: .15; }

.toast__content { display: grid; gap: 6px; }
.toast__title { font-weight: 700; line-height: 1.2; font-size: 0.98rem; }
.toast__message { opacity: 0.98; line-height: 1.35; font-size: 0.95rem; }

.toast__actions { margin-top: 4px; display: flex; gap: 8px; flex-wrap: wrap; }
.toast__btn {
  appearance: none; border: 0; outline: 0; cursor: pointer;
  font-weight: 600; text-decoration: none; color: inherit;
  padding: 6px 10px; border-radius: 10px;
  background: rgba(255,255,255,0.10);
}
.toast__btn:hover { background: rgba(255,255,255,0.16); }

.toast__close {
  appearance: none; border: 0; cursor: pointer; background: transparent;
  color: inherit; font-size: 18px; line-height: 1; opacity: .7; margin-top: 2px;
}
.toast__close:hover { opacity: 1; }

/* Progress bar uses accent */
.toast__progress {
  height: 3px;
  border-radius: 999px;
  background: var(--toast-accent);
  opacity: .75;
  transform-origin: left center;
  animation: toast-bar linear var(--toast-duration) forwards;
}
@keyframes toast-bar { from { width: 100%; } to { width: 0%; } }

/* Light mode tuning (optional) */
@media (prefers-color-scheme: light) {
  .toast {
    --toast-bg: rgba(255,255,255,0.92);
    --toast-fg: #0b1220;
    --toast-border: rgba(15, 23, 42, 0.12);
    --toast-shadow:
      0 14px 34px rgba(2,6,23,.12),
      0 6px 14px rgba(2,6,23,.10);
  }
}

/* Mobile handling */
@media (max-width: 640px) {
  .toast { --toast-max-width: 94vw; padding: 12px 12px 10px 12px; }
  .toast__title { font-size: 0.95rem; }
  .toast__message { font-size: 0.92rem; }
}
</style>
