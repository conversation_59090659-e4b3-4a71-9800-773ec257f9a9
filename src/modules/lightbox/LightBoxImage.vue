<!-- LightboxImage.vue -->
<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

const props = defineProps({
  src: { type: String, required: true },
  alt: { type: String, default: '' }
})
const open = ref(false)

function onKey(e) { if (e.key === 'Escape') open.value = false }
onMounted(() => window.addEventListener('keydown', onKey))
onBeforeUnmount(() => window.removeEventListener('keydown', onKey))
</script>

<template>
  <!-- Trigger (thumbnail) -->
  <div @click="open = true" class="cursor-zoom-in inline-block">
    <img :src="src" :alt="alt" class="h-32 w-auto object-contain rounded-md border border-gray-200" />
  </div>

  <!-- Modal overlay -->
  <div
    v-if="open"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70"
    @click.self="open = false"
    aria-modal="true"
    role="dialog"
  >
    <button
      class="absolute top-4 right-4 rounded-full bg-white/90 px-3 py-1 text-sm shadow"
      @click="open = false"
      aria-label="Close"
    >
      ✕
    </button>

    <!-- Viewport-sized box; image will fill as much as possible while keeping aspect ratio -->
    <div class="w-[85vw] h-[75vh] flex items-center justify-center">
      <img
        :src="src"
        :alt="alt"
        class="max-w-full max-h-full w-full h-full object-contain rounded"
        @click.stop
      />
    </div>
  </div>
</template>
