<template>
  <span
    class="chip"
    :class="[sizeClass, shapeClass, variantClass, block ? 'chip--block' : '']"
    :style="paletteStyle"
    :title="title || undefined"
  >
    <slot name="icon-left" />
    <span class="chip__label"><slot>{{ label }}</slot></span>
    <slot name="icon-right" />
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

type Size = 'sm' | 'md' | 'lg'

defineOptions({ name: 'AisyngChip' })

const props = withDefaults(defineProps<{
  /** Text to render if no default slot is provided */
  label?: string
  /** Tooltip/title attribute */
  title?: string
  /** Visual size */
  size?: Size
  /** Fully rounded (pill) vs slightly rounded */
  pill?: boolean
  /** Visual style */
  variant?: 'soft' | 'outline' | 'solid'
  /** Palette name or 'custom' */
  color?: 'teal' | 'gray' | 'indigo' | 'amber' | 'red' | 'green' | 'purple' | 'blue' | 'custom'
  /** Provide when color='custom' */
  custom?: { base: string; dark?: string }
  /** Make chip expand to fill its container's inline axis */
  block?: boolean
}>(), {
  size: 'md',
  pill: true,
  variant: 'soft',
  color: 'teal',
  block: false,
})

// Palette via CSS vars to avoid Tailwind JIT pitfalls
const paletteStyle = computed(() => {
  const m: Record<string, { base: string; dark: string }> = {
    teal:   { base: 'var(--amf-primary, #008891)', dark: 'var(--amf-primary-dark, #005f73)' },
    gray:   { base: '#6b7280', dark: '#374151' },
    indigo: { base: '#4f46e5', dark: '#3730a3' },
    amber:  { base: '#f59e0b', dark: '#b45309' },
    red:    { base: '#ef4444', dark: '#b91c1c' },
    green:  { base: '#22c55e', dark: '#15803d' },
    purple: { base: '#8b5cf6', dark: '#6d28d9' },
    blue:   { base: '#3b82f6', dark: '#1d4ed8' },
  }
  const chosen = props.color === 'custom' && props.custom
    ? { base: props.custom.base, dark: props.custom.dark || props.custom.base }
    : (m[props.color!] || m.teal)
  return {
    '--chip-base': chosen.base,
    '--chip-base-dark': chosen.dark,
  } as Record<string, string>
})

const sizeClass = computed(() => ({ sm: 'chip--sm', md: 'chip--md', lg: 'chip--lg' })[props.size])
const shapeClass = computed(() => (props.pill ? 'chip--pill' : 'chip--rounded'))
const variantClass = computed(() => ({ soft: 'chip--soft', outline: 'chip--outline', solid: 'chip--solid' })[props.variant])
</script>

<style scoped>
.chip {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  line-height: 1;
  user-select: none;
  border-width: 1px;
  border-style: solid;
  cursor: default;
  transition: background-color .15s ease, border-color .15s ease, color .15s ease;
}
.chip--block { display: flex; justify-content: center; width: 100%; }
.chip__label { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }

/* Sizes */
.chip--sm { padding: 0.25rem 0.5rem; font-size: 0.72rem; }
.chip--md { padding: 0.375rem 0.75rem; font-size: 0.85rem; }
.chip--lg { padding: 0.5rem 1rem; font-size: 0.98rem; }

/* Shape */
.chip--pill { border-radius: 9999px; }
.chip--rounded { border-radius: 12px; }

/* Variants */
.chip--soft {
  /* fallback minimal before color-mix */
  background: rgba(0,0,0,0.02);
  background: color-mix(in srgb, var(--chip-base) 10%, #fff);
  border-color: color-mix(in srgb, var(--chip-base) 30%, transparent);
  color: color-mix(in srgb, var(--chip-base-dark) 65%, #334155);
}
.chip--outline {
  background: #fff;
  border-color: var(--chip-base);
  color: var(--chip-base);
}
.chip--solid {
  background: var(--chip-base);
  border-color: var(--chip-base);
  color: #fff;
}

/* Gentle hover polish (non-interactive, but looks nice) */
.chip--soft:hover    { background: color-mix(in srgb, var(--chip-base) 14%, #fff); }
.chip--outline:hover { background: color-mix(in srgb, var(--chip-base) 10%, #fff); }
.chip--solid:hover   { filter: brightness(0.98); }
</style>
