// ============================
// FilterChips.vue (Generic)
// Reusable, accessible chip group with single- or multi-select
// ============================
<template>
  <div
    :role="multiple ? 'group' : 'radiogroup'"
    class="flex flex-wrap items-center justify-center gap-3"
    :aria-label="ariaLabel"
  >
    <button
      v-for="(option, idx) in options"
      :key="optionKey(option, idx)"
      type="button"
      :ref="el => (btnRefs[idx] = el)"
      :disabled="disabled || !!option.disabled"
      @click="onClick(option.value)"
      @keydown="onKeydown($event, idx)"
      :role="multiple ? 'button' : 'radio'"
      :aria-pressed="multiple ? isActive(option.value) : undefined"
      :aria-checked="!multiple ? isActive(option.value) : undefined"
      :tabindex="tabIndexFor(idx, option.value)"
      :class="[
        buttonClass,
        isActive(option.value) ? activeClass : inactiveClass,
        (disabled || option.disabled) ? 'opacity-60 cursor-not-allowed' : ''
      ]"
    >
      <slot name="chip" :option="option">{{ option.label }}</slot>
    </button>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

/**
 * Props
 * - modelValue: string | number | (string|number)[] depending on `multiple`
 * - options: Array<{ label: string, value: any, disabled?: boolean }>
 * - multiple: enable multi-select (v-model becomes an array)
 * - disabled: disable the entire control
 * - ariaLabel: a11y label for the group
 * - buttonClass / activeClass / inactiveClass: styling hooks
 */
const props = defineProps({
  modelValue: { type: [String, Number, Array], default: '' },
  options: {
    type: Array,
    default: () => []
  },
  multiple: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
  ariaLabel: { type: String, default: 'Filters' },
  buttonClass: {
    type: String,
    default:
      'px-5 py-2 rounded-full font-semibold border transition focus:outline-none focus:ring-2 focus:ring-offset-1'
  },
  activeClass: {
    type: String,
    default: 'bg-teal-600 text-white border-teal-600 shadow'
  },
  inactiveClass: {
    type: String,
    default: 'bg-white text-teal-700 border-teal-700 hover:bg-teal-50'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const btnRefs = ref([])

function toArray(v) {
  return Array.isArray(v) ? v.slice() : (v === undefined || v === null || v === '' ? [] : [v])
}

// normalize local state based on `multiple`
let local = props.multiple ? toArray(props.modelValue) : props.modelValue

watch(
  () => props.modelValue,
  v => {
    local = props.multiple ? toArray(v) : v
  }
)

watch(
  () => props.multiple,
  m => {
    local = m ? toArray(local) : (Array.isArray(local) ? local[0] ?? '' : local)
  }
)

function isActive(val) {
  return props.multiple ? toArray(local).includes(val) : local === val
}

function onClick(val) {
  if (props.disabled) return
  if (props.multiple) {
    const next = toArray(local)
    const i = next.indexOf(val)
    if (i >= 0) next.splice(i, 1)
    else next.push(val)
    local = next
    emit('update:modelValue', next)
    emit('change', next)
  } else {
    if (local === val) return
    local = val
    emit('update:modelValue', val)
    emit('change', val)
  }
}

function onKeydown(e, idx) {
  const max = props.options.length - 1
  const move = offset => {
    const next = (idx + offset + props.options.length) % props.options.length
    btnRefs.value[next]?.focus()
  }
  switch (e.key) {
    case 'ArrowRight':
    case 'ArrowDown':
      e.preventDefault(); move(1); break
    case 'ArrowLeft':
    case 'ArrowUp':
      e.preventDefault(); move(-1); break
    case 'Home':
      e.preventDefault(); btnRefs.value[0]?.focus(); break
    case 'End':
      e.preventDefault(); btnRefs.value[max]?.focus(); break
    case ' ':
    case 'Enter':
      e.preventDefault(); btnRefs.value[idx]?.click(); break
  }
}

function tabIndexFor(idx) {
  if (props.multiple) return 0
  const activeIdx = props.options.findIndex(o => o.value === local)
  return activeIdx === -1 ? (idx === 0 ? 0 : -1) : (idx === activeIdx ? 0 : -1)
}

function optionKey(option, idx) {
  return option && Object.prototype.hasOwnProperty.call(option, 'value') ? option.value : idx
}
</script>

<style scoped>
/* Optional: fine-tune focus ring for chips */
button:focus-visible { outline: none; }
</style>
