@import "tailwindcss";

/* 1. == Design Tokens & Theme Variables == */
@theme {
  --color-amf-primary: #008891;
  --color-amf-primary-dark: #005f73;
  --color-amf-primary-light: #e0f4fb;
  --color-amf-accent: #003049;
  --color-amf-bg: #f5fafd;
  --color-amf-card-bg: #ffffff;
  --font-amf-inter: 'Inter', Arial, sans-serif;
  --radius-amf: 18px;
  --shadow-amf: 0 4px 18px rgba(0, 0, 0, 0.06);
}
:root {
  /* AMF teal palette */
  --amf-primary: #008891;
  --amf-primary-dark: #005f73;

  /* (optional) extras you might use later */
  --amf-focus: #5eead4;        /* teal-300-ish */
  --amf-shadow: 0 6px 26px rgba(0,137,145,0.08);

  /* TEAL (AMF default) */
  --chips-teal-base: var(--amf-primary, #008891);
  --chips-teal-dark: var(--amf-primary-dark, #005f73);

  /* GRAY */
  --chips-gray-base: #6b7280;   /* gray-500 */
  --chips-gray-dark: #374151;   /* gray-700 */

  /* INDIGO */
  --chips-indigo-base: #4f46e5; /* indigo-600 */
  --chips-indigo-dark: #3730a3; /* indigo-700 */

  /* AMBER */
  --chips-amber-base: #f59e0b;  /* amber-500 */
  --chips-amber-dark: #b45309;  /* amber-700 */

  /* RED */
  --chips-red-base: #ef4444;    /* red-500 */
  --chips-red-dark: #b91c1c;    /* red-700 */

  /* GREEN */
  --chips-green-base: #22c55e;  /* green-500 */
  --chips-green-dark: #15803d;  /* green-700 */

  /* PURPLE (violet) */
  --chips-purple-base: #8b5cf6; /* violet-500 */
  --chips-purple-dark: #6d28d9; /* violet-700 */

  /* BLUE */
  --chips-blue-base: #3b82f6;   /* blue-500 */
  --chips-blue-dark: #1d4ed8;   /* blue-700 */
}

/* 2. Core Styles */
html { scroll-behavior: smooth; }
body {
  font-family: var(--font-amf-inter);
  background-color: var(--color-amf-bg);
  color: var(--color-amf-accent);
  font-size: 1.06rem;
  line-height: 1.6;
}

/* 3. Custom Utilities (with hover support) */
@utility amf-bg-primary {
  @apply bg-[var(--color-amf-primary)];
  @variant hover {
    @apply bg-[var(--color-amf-primary-dark)];
  }
}
@utility amf-bg-primary-light {
  @apply bg-[var(--color-amf-primary-light)];
  @variant hover {
    @apply bg-[var(--color-amf-primary)];
  }
}
@utility amf-bg-accent {
  @apply bg-[var(--color-amf-accent)];
}
@utility amf-bg-card {
  @apply bg-[var(--color-amf-card-bg)];
}
@utility amf-text-primary {
  @apply text-[var(--color-amf-primary)];
}
@utility amf-text-primary-dark {
  @apply text-[var(--color-amf-primary-dark)];
}
@utility amf-rounded {
  @apply rounded-[var(--radius-amf)];
}
@utility amf-shadow {
  @apply shadow-[var(--shadow-amf)];
}

/* 4. Tailwind Layers */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 5. Hero Styles */
.amf-hero {
  background: linear-gradient(90deg, rgba(0,89,145,0.68), rgba(0,89,145,0.40)),
              url('../assets/bg3.png') center/cover no-repeat;
  color: #fff;
  text-align: center;
  padding: 1rem 1rem 4.5rem;
  position: relative;
}
@media (max-width: 600px) {
  .amf-hero { padding: 1rem 0.3rem 2rem; }
}
.amf-hero h1 { font-size: 2.5rem; font-weight: 900; margin-bottom: 0.6rem; letter-spacing: -1px; }
.amf-hero h2 { font-size: 1.15rem; font-weight: 600; margin-bottom: 0.5rem; }
.amf-hero .lead { font-size: 1.08rem; margin-bottom: 1.7rem; }
.amf-hero .amf-hero-cta {
  background: var(--color-amf-primary);
  color: #fff;
  font-weight: 700;
  border: none;
  padding: 0.95rem 2.2rem;
  font-size: 1.13rem;
  border-radius: 9px;
  margin-top: 1.6rem;
  box-shadow: 0 2px 16px rgba(0,137,145,0.11);
  cursor: pointer;
  transition: background 0.2s;
}
.amf-hero .amf-hero-cta:hover {
  background: var(--color-amf-primary-dark);
}

/* 6. Footer Styles */
.amf-footer {
  background-color: var(--color-amf-accent);
  color: #fff;
  padding: 2.3rem 1rem 1.3rem;
  font-size: 0.97rem;
  margin-top: 2.2rem;
  text-align: center;
}
.amf-footer-links a {
  color: #fff;
  opacity: 0.88;
  margin-right: 0.5rem;
  transition: opacity 0.15s;
}
.amf-footer-links a:hover {
  opacity: 1;
  text-decoration: underline;
}
