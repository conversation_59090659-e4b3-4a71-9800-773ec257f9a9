
FROM node:lts-alpine as build-stage

ENV VITE_API_BASE_URL=https://aisyng-aurora-server-541484868075.europe-west9.run.app

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

# Replace the line in config.js
RUN sed -i 's|export const API_BASE_URL = .*|export const API_BASE_URL = "https://aisyng-aurora-server-541484868075.europe-west9.run.app";|' src/config.js

RUN npm run build

# production stage
FROM nginx:stable-alpine as production-stage

COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
