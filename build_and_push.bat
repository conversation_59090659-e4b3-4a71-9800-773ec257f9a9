docker build -t europe-west9-docker.pkg.dev/amf-formation/amf-repository/aisyng-aurora-web:latest .
docker push europe-west9-docker.pkg.dev/amf-formation/amf-repository/aisyng-aurora-web:latest
gcloud run deploy aisyng-aurora-web --image europe-west9-docker.pkg.dev/amf-formation/amf-repository/aisyng-aurora-web:latest --region europe-west9 --platform managed --min-instances 0 --max-instances 5 --memory 256M --cpu 1 --port 80
